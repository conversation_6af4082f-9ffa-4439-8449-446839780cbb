import {
  __require
} from "./chunk-PR4QN5HX.js";

// ../node_modules/.bun/tailwind-variants@2.1.0+1a2605b6c710f71e/node_modules/tailwind-variants/dist/chunk-HK3UQQ6K.js
var u = ((e) => typeof __require < "u" ? __require : typeof Proxy < "u" ? new Proxy(e, { get: (t, n) => (typeof __require < "u" ? __require : t)[n] }) : e)(function(e) {
  if (typeof __require < "u") return __require.apply(this, arguments);
  throw Error('Dynamic require of "' + e + '" is not supported');
});
var y = (e) => e === false ? "false" : e === true ? "true" : e === 0 ? "0" : e;
var p = (e) => {
  if (!e || typeof e != "object") return true;
  for (let t in e) return false;
  return true;
};
var g = (e, t) => {
  if (e === t) return true;
  if (!e || !t) return false;
  let n = Object.keys(e), r2 = Object.keys(t);
  if (n.length !== r2.length) return false;
  for (let s2 = 0; s2 < n.length; s2++) {
    let f = n[s2];
    if (!r2.includes(f) || e[f] !== t[f]) return false;
  }
  return true;
};
function o(e, t) {
  for (let n = 0; n < e.length; n++) {
    let r2 = e[n];
    Array.isArray(r2) ? o(r2, t) : t.push(r2);
  }
}
var i = (...e) => {
  let t = [];
  o(e, t);
  let n = [];
  for (let r2 = 0; r2 < t.length; r2++) t[r2] && n.push(t[r2]);
  return n;
};
var c = (e, t) => {
  let n = {};
  for (let r2 in e) {
    let s2 = e[r2];
    if (r2 in t) {
      let f = t[r2];
      Array.isArray(s2) || Array.isArray(f) ? n[r2] = i(f, s2) : typeof s2 == "object" && typeof f == "object" && s2 && f ? n[r2] = c(s2, f) : n[r2] = f + " " + s2;
    } else n[r2] = s2;
  }
  for (let r2 in t) r2 in e || (n[r2] = t[r2]);
  return n;
};
var l = /\s+/g;
var h = (e) => !e || typeof e != "string" ? e : e.replace(l, " ").trim();

// ../node_modules/.bun/tailwind-variants@2.1.0+1a2605b6c710f71e/node_modules/tailwind-variants/dist/chunk-2DBF3ZSA.js
var r = null;
var s = null;
var p2 = async () => r || s || (s = import("./tailwind-merge.js").then((t) => (r = t, t)).catch(() => null), s);
var m = (t) => (n) => {
  if (s && !r) return n;
  if (r) {
    let { twMerge: e, extendTailwindMerge: l2 } = r;
    return (p(t) ? e : l2({ ...t, extend: { theme: t.theme, classGroups: t.classGroups, conflictingClassGroupModifiers: t.conflictingClassGroupModifiers, conflictingClassGroups: t.conflictingClassGroups, ...t.extend } }))(n);
  }
  try {
    let { twMerge: e, extendTailwindMerge: l2 } = u("tailwind-merge");
    return r = { twMerge: e, extendTailwindMerge: l2 }, (p(t) ? e : l2({ ...t, extend: { theme: t.theme, classGroups: t.classGroups, conflictingClassGroupModifiers: t.conflictingClassGroupModifiers, conflictingClassGroups: t.conflictingClassGroups, ...t.extend } }))(n);
  } catch {
    return p2(), n;
  }
};

// ../node_modules/.bun/tailwind-variants@2.1.0+1a2605b6c710f71e/node_modules/tailwind-variants/dist/index.js
var st = { twMerge: true, twMergeConfig: {}, responsiveVariants: false };
var x = (...l2) => {
  let u2 = [];
  X(l2, u2);
  let t = "";
  for (let d = 0; d < u2.length; d++) u2[d] && (t && (t += " "), t += u2[d]);
  return t || void 0;
};
function X(l2, u2) {
  for (let t = 0; t < l2.length; t++) {
    let d = l2[t];
    Array.isArray(d) ? X(d, u2) : d && u2.push(d);
  }
}
var P = null;
var B = {};
var F = false;
var S = (...l2) => (u2) => {
  let t = x(l2);
  return !t || !u2.twMerge ? t : ((!P || F) && (F = false, P = m(B)), P(t) || void 0);
};
var Q = (l2, u2) => {
  for (let t in u2) t in l2 ? l2[t] = x(l2[t], u2[t]) : l2[t] = u2[t];
  return l2;
};
var rt = (l2, u2) => {
  let { extend: t = null, slots: d$1 = {}, variants: R = {}, compoundVariants: q = [], compoundSlots: A = [], defaultVariants: L = {} } = l2, m2 = { ...st, ...u2 }, M = t?.base ? x(t.base, l2?.base) : l2?.base, y2 = t?.variants && !p(t.variants) ? c(R, t.variants) : R, T = t?.defaultVariants && !p(t.defaultVariants) ? { ...t.defaultVariants, ...L } : L;
  !p(m2.twMergeConfig) && !g(m2.twMergeConfig, B) && (F = true, B = m2.twMergeConfig);
  let j = p(t?.slots), $ = p(d$1) ? {} : { base: x(l2?.base, j && t?.base), ...d$1 }, N = j ? $ : Q({ ...t?.slots }, p($) ? { base: l2?.base } : $), w = p(t?.compoundVariants) ? q : i(t?.compoundVariants, q), V = (b$1) => {
    if (p(y2) && p(d$1) && j) return S(M, b$1?.class, b$1?.className)(m2);
    if (w && !Array.isArray(w)) throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof w}`);
    if (A && !Array.isArray(A)) throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof A}`);
    let Z = (n, e, s2 = [], o2) => {
      let a = s2;
      if (typeof e == "string") {
        let c2 = h(e).split(" ");
        for (let f = 0; f < c2.length; f++) a.push(`${n}:${c2[f]}`);
      } else if (Array.isArray(e)) for (let r2 = 0; r2 < e.length; r2++) a.push(`${n}:${e[r2]}`);
      else if (typeof e == "object" && typeof o2 == "string" && o2 in e) {
        let r2 = e[o2];
        if (r2 && typeof r2 == "string") {
          let f = h(r2).split(" "), p3 = [];
          for (let i2 = 0; i2 < f.length; i2++) p3.push(`${n}:${f[i2]}`);
          a[o2] = a[o2] ? a[o2].concat(p3) : p3;
        } else if (Array.isArray(r2) && r2.length > 0) {
          let c2 = [];
          for (let f = 0; f < r2.length; f++) c2.push(`${n}:${r2[f]}`);
          a[o2] = c2;
        }
      }
      return a;
    }, U = (n, e = y2, s2 = null, o2 = null) => {
      let a = e[n];
      if (!a || p(a)) return null;
      let r2 = o2?.[n] ?? b$1?.[n];
      if (r2 === null) return null;
      let c$1 = y(r2), f = Array.isArray(m2.responsiveVariants) && m2.responsiveVariants.length > 0 || m2.responsiveVariants === true, p3 = T?.[n], i2 = [];
      if (typeof c$1 == "object" && f) for (let [C, G] of Object.entries(c$1)) {
        let nt = a[G];
        if (C === "initial") {
          p3 = G;
          continue;
        }
        Array.isArray(m2.responsiveVariants) && !m2.responsiveVariants.includes(C) || (i2 = Z(C, nt, i2, s2));
      }
      let v = c$1 != null && typeof c$1 != "object" ? c$1 : y(p3), h2 = a[v || "false"];
      return typeof i2 == "object" && typeof s2 == "string" && i2[s2] ? Q(i2, h2) : i2.length > 0 ? (i2.push(h2), s2 === "base" ? i2.join(" ") : i2) : h2;
    }, _ = () => {
      if (!y2) return null;
      let n = Object.keys(y2), e = [];
      for (let s2 = 0; s2 < n.length; s2++) {
        let o2 = U(n[s2], y2);
        o2 && e.push(o2);
      }
      return e;
    }, K = (n, e) => {
      if (!y2 || typeof y2 != "object") return null;
      let s2 = [];
      for (let o2 in y2) {
        let a = U(o2, y2, n, e), r2 = n === "base" && typeof a == "string" ? a : a && a[n];
        r2 && s2.push(r2);
      }
      return s2;
    }, W = {};
    for (let n in b$1) {
      let e = b$1[n];
      e !== void 0 && (W[n] = e);
    }
    let z = (n, e) => {
      let s2 = typeof b$1?.[n] == "object" ? { [n]: b$1[n]?.initial } : {};
      return { ...T, ...W, ...s2, ...e };
    }, D = (n = [], e) => {
      let s2 = [], o2 = n.length;
      for (let a = 0; a < o2; a++) {
        let { class: r2, className: c2, ...f } = n[a], p3 = true, i2 = z(null, e);
        for (let v in f) {
          let h2 = f[v], C = i2[v];
          if (Array.isArray(h2)) {
            if (!h2.includes(C)) {
              p3 = false;
              break;
            }
          } else {
            if ((h2 == null || h2 === false) && (C == null || C === false)) continue;
            if (C !== h2) {
              p3 = false;
              break;
            }
          }
        }
        p3 && (r2 && s2.push(r2), c2 && s2.push(c2));
      }
      return s2;
    }, tt = (n) => {
      let e = D(w, n);
      if (!Array.isArray(e)) return e;
      let s2 = {}, o2 = S;
      for (let a = 0; a < e.length; a++) {
        let r2 = e[a];
        if (typeof r2 == "string") s2.base = o2(s2.base, r2)(m2);
        else if (typeof r2 == "object") for (let c2 in r2) s2[c2] = o2(s2[c2], r2[c2])(m2);
      }
      return s2;
    }, et = (n) => {
      if (A.length < 1) return null;
      let e = {}, s2 = z(null, n);
      for (let o2 = 0; o2 < A.length; o2++) {
        let { slots: a = [], class: r2, className: c$1, ...f } = A[o2];
        if (!p(f)) {
          let p3 = true;
          for (let i2 in f) {
            let v = s2[i2], h2 = f[i2];
            if (v === void 0 || (Array.isArray(h2) ? !h2.includes(v) : h2 !== v)) {
              p3 = false;
              break;
            }
          }
          if (!p3) continue;
        }
        for (let p3 = 0; p3 < a.length; p3++) {
          let i2 = a[p3];
          e[i2] || (e[i2] = []), e[i2].push([r2, c$1]);
        }
      }
      return e;
    };
    if (!p(d$1) || !j) {
      let n = {};
      if (typeof N == "object" && !p(N)) {
        let e = S;
        for (let s2 in N) n[s2] = (o2) => {
          let a = tt(o2), r2 = et(o2);
          return e(N[s2], K(s2, o2), a ? a[s2] : void 0, r2 ? r2[s2] : void 0, o2?.class, o2?.className)(m2);
        };
      }
      return n;
    }
    return S(M, _(), D(w), b$1?.class, b$1?.className)(m2);
  }, Y = () => {
    if (!(!y2 || typeof y2 != "object")) return Object.keys(y2);
  };
  return V.variantKeys = Y(), V.extend = t, V.base = M, V.slots = N, V.variants = y2, V.defaultVariants = T, V.compoundSlots = A, V.compoundVariants = w, V;
};
var it = (l2) => (u2, t) => rt(u2, t ? c(l2, t) : l2);
export {
  S as cn,
  x as cnBase,
  it as createTV,
  st as defaultConfig,
  rt as tv
};
//# sourceMappingURL=tailwind-variants.js.map
