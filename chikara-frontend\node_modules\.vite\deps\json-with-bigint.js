import "./chunk-PR4QN5HX.js";

// ../node_modules/.bun/json-with-bigint@3.4.4/node_modules/json-with-bigint/json-with-bigint.js
var noiseValue = /^-?\d+n+$/;
var originalStringify = JSON.stringify;
var originalParse = JSON.parse;
var JSONStringify = (value, replacer, space) => {
  if ("rawJSON" in JSON) {
    return originalStringify(
      value,
      (key, value2) => {
        if (typeof value2 === "bigint") return JSON.rawJSON(value2.toString());
        if (typeof replacer === "function") return replacer(key, value2);
        if (Array.isArray(replacer) && replacer.includes(key)) return value2;
        return value2;
      },
      space
    );
  }
  if (!value) return originalStringify(value, replacer, space);
  const bigInts = /([\[:])?"(-?\d+)n"($|([\\n]|\s)*(\s|[\\n])*[,\}\]])/g;
  const noise = /([\[:])?("-?\d+n+)n("$|"([\\n]|\s)*(\s|[\\n])*[,\}\]])/g;
  const convertedToCustomJSON = originalStringify(
    value,
    (key, value2) => {
      const isNoise = typeof value2 === "string" && Boolean(value2.match(noiseValue));
      if (isNoise) return value2.toString() + "n";
      if (typeof value2 === "bigint") return value2.toString() + "n";
      if (typeof replacer === "function") return replacer(key, value2);
      if (Array.isArray(replacer) && replacer.includes(key)) return value2;
      return value2;
    },
    space
  );
  const processedJSON = convertedToCustomJSON.replace(bigInts, "$1$2$3");
  const denoisedJSON = processedJSON.replace(noise, "$1$2$3");
  return denoisedJSON;
};
var JSONParse = (text, reviver) => {
  if (!text) return originalParse(text, reviver);
  const MAX_INT = Number.MAX_SAFE_INTEGER.toString();
  const MAX_DIGITS = MAX_INT.length;
  const stringsOrLargeNumbers = /"(?:\\.|[^"])*"|-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][+-]?[0-9]+)?/g;
  const noiseValueWithQuotes = /^"-?\d+n+"$/;
  const customFormat = /^-?\d+n$/;
  const serializedData = text.replace(
    stringsOrLargeNumbers,
    (text2, digits, fractional, exponential) => {
      const isString = text2[0] === '"';
      const isNoise = isString && Boolean(text2.match(noiseValueWithQuotes));
      if (isNoise) return text2.substring(0, text2.length - 1) + 'n"';
      const isFractionalOrExponential = fractional || exponential;
      const isLessThanMaxSafeInt = digits && (digits.length < MAX_DIGITS || digits.length === MAX_DIGITS && digits <= MAX_INT);
      if (isString || isFractionalOrExponential || isLessThanMaxSafeInt)
        return text2;
      return '"' + text2 + 'n"';
    }
  );
  return originalParse(serializedData, (key, value, context) => {
    const isCustomFormatBigInt = typeof value === "string" && Boolean(value.match(customFormat));
    if (isCustomFormatBigInt)
      return BigInt(value.substring(0, value.length - 1));
    const isNoiseValue = typeof value === "string" && Boolean(value.match(noiseValue));
    if (isNoiseValue) return value.substring(0, value.length - 1);
    if (typeof reviver !== "function") return value;
    return reviver(key, value, context);
  });
};
export {
  JSONParse,
  JSONStringify
};
//# sourceMappingURL=json-with-bigint.js.map
