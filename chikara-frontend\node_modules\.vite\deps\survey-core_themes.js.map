{"version": 3, "sources": ["../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/default-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/default-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/default-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/default-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/sharp-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/sharp-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/sharp-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/sharp-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/borderless-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/borderless-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/borderless-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/borderless-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/flat-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/flat-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/flat-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/flat-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/plain-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/plain-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/plain-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/plain-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/doubleborder-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/doubleborder-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/doubleborder-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/doubleborder-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/layered-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/layered-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/layered-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/layered-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/solid-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/solid-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/solid-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/solid-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/threedimensional-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/threedimensional-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/threedimensional-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/threedimensional-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/contrast-light.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/contrast-dark.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/contrast-light-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/contrast-dark-panelless.ts", "../../../../node_modules/.bun/survey-core@2.3.1/node_modules/src/themes/index.ts"], "sourcesContent": ["export default {\n  \"themeName\": \"default\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(249, 249, 249, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(20, 164, 139, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 1px 2px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 1px 2px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.09)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"default\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(43, 43, 43, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(46, 46, 46, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(255, 170, 24, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 1px 2px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"inset 0px 1px 2px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"default\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(249, 249, 249, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(20, 164, 139, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 1px 2px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 1px 2px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.09)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"default\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(43, 43, 43, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(46, 46, 46, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(255, 170, 24, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 1px 2px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"inset 0px 1px 2px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"sharp\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(228, 228, 228, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(224, 224, 224, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(238, 238, 238, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(220, 220, 220, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.6)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.6)\",\n    \"--sjs-primary-backcolor\": \"rgba(103, 58, 176, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(103, 58, 176, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(69, 24, 142, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.25)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.25)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.25)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.25)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"sharp\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(58, 58, 58, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(27, 27, 27, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(33, 33, 33, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.5)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.8)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.55)\",\n    \"--sjs-primary-backcolor\": \"rgba(16, 226, 255, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(0, 0, 0, 0.35)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(129, 240, 255, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.28)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.28)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.25),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.22)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.22)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.22)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.22)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"sharp\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(228, 228, 228, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(238, 238, 238, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(220, 220, 220, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.6)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.6)\",\n    \"--sjs-primary-backcolor\": \"rgba(103, 58, 176, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(103, 58, 176, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(69, 24, 142, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.25)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.25)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.25)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.25)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"sharp\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(58, 58, 58, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(33, 33, 33, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.5)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.8)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.55)\",\n    \"--sjs-primary-backcolor\": \"rgba(16, 226, 255, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(0, 0, 0, 0.35)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(129, 240, 255, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.28)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.28)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.25),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.22)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.22)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.22)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.22)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"borderless\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(241, 246, 255, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(231, 240, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(238, 245, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(223, 233, 250, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(133, 154, 186, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(133, 154, 186, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(35, 101, 200, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(35, 101, 200, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(26, 86, 175, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-light\": \"rgba(220, 229, 241, 1)\",\n    \"--sjs-border-default\": \"rgba(179, 200, 229, 1)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"borderless\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(43, 48, 63, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(50, 55, 72, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(33, 37, 51, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(36, 41, 55, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(40, 46, 61, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(114, 120, 137, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(114, 120, 137, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(114, 187, 255, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(114, 187, 255, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(151, 205, 255, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-light\": \"rgba(55, 62, 79, 1)\",\n    \"--sjs-border-default\": \"rgba(65, 72, 90, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"borderless\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(231, 240, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(220, 232, 252, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(231, 240, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(220, 232, 252, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(133, 154, 186, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(133, 154, 186, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(35, 101, 200, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(35, 101, 200, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(26, 86, 175, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-light\": \"rgba(220, 229, 241, 1)\",\n    \"--sjs-border-default\": \"rgba(179, 200, 229, 1)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"borderless\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(43, 48, 63, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(50, 55, 72, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(43, 48, 63, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(36, 41, 55, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(40, 46, 61, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(114, 120, 137, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(114, 120, 137, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(114, 187, 255, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(114, 187, 255, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(151, 205, 255, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.35)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-light\": \"rgba(55, 62, 79, 1)\",\n    \"--sjs-border-default\": \"rgba(65, 72, 90, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"flat\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(246, 246, 246, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(235, 235, 235, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(235, 235, 235, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(85, 181, 52, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(85, 181, 52, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(64, 159, 31, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.1),0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.12)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.12)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.12)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"flat\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(56, 56, 56, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(43, 43, 43, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(92, 214, 49, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(130, 237, 92, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.09)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.09)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.07)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.07)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"flat\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(246, 246, 246, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(235, 235, 235, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(246, 246, 246, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(235, 235, 235, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(85, 181, 52, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(85, 181, 52, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(64, 159, 31, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.1),0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.12)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.12)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.12)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"flat\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(56, 56, 56, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(43, 43, 43, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(92, 214, 49, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(130, 237, 92, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.09)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.09)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.07)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.07)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.07)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"plain\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(37, 137, 229, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(37, 137, 229, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(21, 119, 209, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.05)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"plain\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(85, 171, 250, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(126, 193, 255, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"plain\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(37, 137, 229, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(37, 137, 229, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(21, 119, 209, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.05)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.15)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"plain\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(36, 36, 36, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(85, 171, 250, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(126, 193, 255, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"doubleborder\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(239, 239, 239, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(245, 245, 245, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(237, 237, 237, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(76, 100, 137, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(76, 100, 137, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(62, 83, 115, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(0, 0, 0, 0.07)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.07)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(0, 0, 0, 0.08),0px 2px 6px 0px rgba(0, 0, 0, 0.04)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.08)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"doubleborder\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(58, 58, 58, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(53, 53, 53, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(120, 156, 210, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(146, 181, 235, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(255, 255, 255, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(255, 255, 255, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.1)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.1)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"doubleborder\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(239, 239, 239, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(245, 245, 245, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(237, 237, 237, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(76, 100, 137, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(76, 100, 137, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(62, 83, 115, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(0, 0, 0, 0.07)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.07)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(0, 0, 0, 0.08),0px 2px 6px 0px rgba(0, 0, 0, 0.04)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.08)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"doubleborder\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(58, 58, 58, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(52, 52, 52, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(47, 47, 47, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(53, 53, 53, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(120, 156, 210, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(146, 181, 235, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.12)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(255, 255, 255, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(255, 255, 255, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.1)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.1)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"layered\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(244, 244, 244, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(216, 207, 236, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(241, 237, 248, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(225, 220, 233, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.5)\",\n    \"--sjs-primary-backcolor\": \"rgba(122, 100, 168, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(122, 100, 168, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(105, 84, 152, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.05),0px 8px 16px 0px rgba(0, 0, 0, 0.1),0px 2px 4px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.05),0px 0px 0px 0px rgba(0, 0, 0, 0.1),0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 0px 0px 1px rgba(0, 0, 0, 0.05),inset 0px 1px 4px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.05),inset 0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.09)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"layered\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(39, 40, 50, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(46, 47, 58, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(30, 31, 40, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(32, 33, 43, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(37, 38, 48, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(213, 215, 238, 1)\",\n    \"--sjs-general-forecolor-light\": \"rgba(117, 120, 140, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(213, 215, 238, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(117, 119, 141, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(164, 127, 243, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(164, 127, 243, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(192, 165, 251, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.1),0px 8px 16px 0px rgba(0, 0, 0, 0.15),0px 2px 4px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.1),0px 0px 0px 0px rgba(0, 0, 0, 0.15),0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-medium\": \"inset 0px 0px 0px 1px rgba(255, 255, 255, 0.05),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.03),inset 0px 1px 4px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.03),inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-light\": \"rgba(54, 56, 69, 1)\",\n    \"--sjs-border-default\": \"rgba(67, 69, 85, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"layered\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(244, 244, 244, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(241, 237, 248, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(225, 220, 233, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.5)\",\n    \"--sjs-primary-backcolor\": \"rgba(122, 100, 168, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(122, 100, 168, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(105, 84, 152, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(0, 0, 0, 0.05),0px 8px 16px 0px rgba(0, 0, 0, 0.1),0px 2px 4px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.05),0px 0px 0px 0px rgba(0, 0, 0, 0.1),0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 0px 0px 1px rgba(0, 0, 0, 0.05),inset 0px 1px 4px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.05),inset 0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.09)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"layered\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(39, 40, 50, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(46, 47, 58, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(39, 40, 50, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(32, 33, 43, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(37, 38, 48, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(213, 215, 238, 1)\",\n    \"--sjs-general-forecolor-light\": \"rgba(117, 120, 140, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(213, 215, 238, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(117, 119, 141, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(164, 127, 243, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(164, 127, 243, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(192, 165, 251, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.1),0px 8px 16px 0px rgba(0, 0, 0, 0.15),0px 2px 4px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.1),0px 0px 0px 0px rgba(0, 0, 0, 0.15),0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-medium\": \"inset 0px 0px 0px 1px rgba(255, 255, 255, 0.05),0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 1px rgba(255, 255, 255, 0.03),inset 0px 1px 4px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(255, 255, 255, 0.03),inset 0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-light\": \"rgba(54, 56, 69, 1)\",\n    \"--sjs-border-default\": \"rgba(67, 69, 85, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"solid\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(46, 172, 180, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(245, 245, 245, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(234, 234, 234, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.43)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.8)\",\n    \"--sjs-primary-backcolor\": \"rgba(46, 172, 180, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(46, 172, 180, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(23, 156, 165, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 2px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.09)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"solid\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(53, 55, 63, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(60, 63, 74, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(38, 40, 47, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(46, 48, 55, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(51, 53, 61, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(125, 129, 143, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(125, 130, 148, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(58, 202, 211, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(67, 70, 80, 1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(92, 224, 233, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px -2px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-border-light\": \"rgba(65, 69, 83, 1)\",\n    \"--sjs-border-default\": \"rgba(97, 101, 118, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"solid\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(243, 243, 243, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(245, 245, 245, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(234, 234, 234, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.43)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(46, 172, 180, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(46, 172, 180, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(23, 156, 165, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"inset 0px 2px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner-reset\": \"inset 0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.09)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"solid\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(53, 55, 63, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(60, 63, 74, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(53, 55, 63, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(46, 48, 55, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(51, 53, 61, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(125, 129, 143, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(125, 130, 148, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(58, 202, 211, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(67, 70, 80, 1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(92, 224, 233, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.2)\",\n    \"--sjs-shadow-inner\": \"0px -2px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(33, 35, 43, 1)\",\n    \"--sjs-border-light\": \"rgba(65, 69, 83, 1)\",\n    \"--sjs-border-default\": \"rgba(97, 101, 118, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"threedimensional\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(242, 242, 242, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(242, 242, 242, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(217, 74, 100, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(217, 74, 100, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(189, 61, 84, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 2px rgba(228, 228, 228, 1),0px 0px 0px 2px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(228, 228, 228, 1),0px 0px 0px 0px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(228, 228, 228, 1),0px 8px 0px 2px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-large\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(228, 228, 228, 1),0px -2px 0px 2px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(228, 228, 228, 1),0px 0px 0px 0px rgba(228, 228, 228, 1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.08)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.08)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"threedimensional\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(38, 38, 38, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(28, 28, 28, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(58, 58, 58, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(243, 87, 134, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.05)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(250, 118, 157, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 2px rgba(64, 64, 64, 1),0px 0px 0px 2px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(64, 64, 64, 1),0px 0px 0px 0px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(64, 64, 64, 1),0px 8px 0px 2px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-large\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(64, 64, 64, 1),0px 2px 0px 2px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(64, 64, 64, 1),0px 0px 0px 0px rgba(64, 64, 64, 1)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"threedimensional\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(242, 242, 242, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(248, 248, 248, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(242, 242, 242, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 0.91)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(217, 74, 100, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(217, 74, 100, 0.1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(189, 61, 84, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 2px rgba(228, 228, 228, 1),0px 0px 0px 2px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(228, 228, 228, 1),0px 0px 0px 0px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(228, 228, 228, 1),0px 8px 0px 2px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-large\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(228, 228, 228, 1),0px -2px 0px 2px rgba(228, 228, 228, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(228, 228, 228, 1),0px 0px 0px 0px rgba(228, 228, 228, 1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.08)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 0.08)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(229, 10, 62, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"threedimensional\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(38, 38, 38, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(28, 28, 28, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(58, 58, 58, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(255, 255, 255, 0.78)\",\n    \"--sjs-general-forecolor-light\": \"rgba(255, 255, 255, 0.42)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 0.79)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 0.45)\",\n    \"--sjs-primary-backcolor\": \"rgba(243, 87, 134, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 0.05)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(250, 118, 157, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(32, 32, 32, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 2px 0px 2px rgba(64, 64, 64, 1),0px 0px 0px 2px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(64, 64, 64, 1),0px 0px 0px 0px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(64, 64, 64, 1),0px 8px 0px 2px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-large\": \"0px 0px 0px 0px rgba(0, 0, 0, 0.1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(64, 64, 64, 1),0px 2px 0px 2px rgba(64, 64, 64, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(64, 64, 64, 1),0px 0px 0px 0px rgba(64, 64, 64, 1)\",\n    \"--sjs-border-light\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-default\": \"rgba(255, 255, 255, 0.12)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(254, 76, 108, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"contrast\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(83, 83, 83, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-large\": \"0px 6px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(0, 0, 0, 1),0px -2px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1),0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(156, 2, 39, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"contrast\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": false,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(53, 53, 53, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-large\": \"0px 6px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(0, 0, 0, 1),0px -2px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1),0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-border-light\": \"rgba(232, 192, 51, 1)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(156, 2, 39, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"contrast\",\n  \"colorPalette\": \"light\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(83, 83, 83, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(255, 255, 255, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-large\": \"0px 6px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(0, 0, 0, 1),0px -2px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1),0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-border-light\": \"rgba(0, 0, 0, 0.2)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-border-inside\": \"rgba(0, 0, 0, 0.16)\",\n    \"--sjs-special-red\": \"rgba(156, 2, 39, 1)\",\n    \"--sjs-special-red-light\": \"rgba(229, 10, 62, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-green\": \"rgba(25, 179, 148, 1)\",\n    \"--sjs-special-green-light\": \"rgba(25, 179, 148, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-blue\": \"rgba(67, 127, 217, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(67, 127, 217, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "export default {\n  \"themeName\": \"contrast\",\n  \"colorPalette\": \"dark\",\n  \"isPanelless\": true,\n  \"cssVariables\": {\n    \"--sjs-general-backcolor\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dark\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim\": \"rgba(255, 216, 77, 1)\",\n    \"--sjs-general-backcolor-dim-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-backcolor-dim-dark\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-general-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-general-dim-forecolor-light\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-primary-backcolor-light\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-backcolor-dark\": \"rgba(53, 53, 53, 1)\",\n    \"--sjs-primary-forecolor\": \"rgba(255, 255, 255, 1)\",\n    \"--sjs-primary-forecolor-light\": \"rgba(32, 32, 32, 0.25)\",\n    \"--sjs-base-unit\": \"8px\",\n    \"--sjs-corner-radius\": \"4px\",\n    \"--sjs-secondary-backcolor\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-secondary-backcolor-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-secondary-backcolor-semi-light\": \"rgba(255, 152, 20, 0.25)\",\n    \"--sjs-secondary-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-secondary-forecolor-light\": \"rgba(48, 48, 48, 0.25)\",\n    \"--sjs-shadow-small\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-small-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-medium\": \"0px 0px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-large\": \"0px 6px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner\": \"0px 0px 0px 2px rgba(0, 0, 0, 1),0px -2px 0px 2px rgba(0, 0, 0, 1)\",\n    \"--sjs-shadow-inner-reset\": \"0px 0px 0px 0px rgba(0, 0, 0, 1),0px 0px 0px 0px rgba(0, 0, 0, 1)\",\n    \"--sjs-border-light\": \"rgba(232, 192, 51, 1)\",\n    \"--sjs-border-default\": \"rgba(0, 0, 0, 1)\",\n    \"--sjs-border-inside\": \"rgba(255, 255, 255, 0.08)\",\n    \"--sjs-special-red\": \"rgba(156, 2, 39, 1)\",\n    \"--sjs-special-red-light\": \"rgba(254, 76, 108, 0.1)\",\n    \"--sjs-special-red-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-green\": \"rgba(36, 197, 164, 1)\",\n    \"--sjs-special-green-light\": \"rgba(36, 197, 164, 0.1)\",\n    \"--sjs-special-green-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-blue\": \"rgba(91, 151, 242, 1)\",\n    \"--sjs-special-blue-light\": \"rgba(91, 151, 242, 0.1)\",\n    \"--sjs-special-blue-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-special-yellow\": \"rgba(255, 152, 20, 1)\",\n    \"--sjs-special-yellow-light\": \"rgba(255, 152, 20, 0.1)\",\n    \"--sjs-special-yellow-forecolor\": \"rgba(48, 48, 48, 1)\",\n    \"--sjs-article-font-xx-large-textDecoration\": \"none\",\n    \"--sjs-article-font-xx-large-fontWeight\": \"700\",\n    \"--sjs-article-font-xx-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-xx-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-xx-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-xx-large-lineHeight\": \"64px\",\n    \"--sjs-article-font-xx-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-xx-large-textCase\": \"none\",\n    \"--sjs-article-font-x-large-textDecoration\": \"none\",\n    \"--sjs-article-font-x-large-fontWeight\": \"700\",\n    \"--sjs-article-font-x-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-x-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-x-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-x-large-lineHeight\": \"56px\",\n    \"--sjs-article-font-x-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-x-large-textCase\": \"none\",\n    \"--sjs-article-font-large-textDecoration\": \"none\",\n    \"--sjs-article-font-large-fontWeight\": \"700\",\n    \"--sjs-article-font-large-fontStyle\": \"normal\",\n    \"--sjs-article-font-large-fontStretch\": \"normal\",\n    \"--sjs-article-font-large-letterSpacing\": \"0\",\n    \"--sjs-article-font-large-lineHeight\": \"40px\",\n    \"--sjs-article-font-large-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-large-textCase\": \"none\",\n    \"--sjs-article-font-medium-textDecoration\": \"none\",\n    \"--sjs-article-font-medium-fontWeight\": \"700\",\n    \"--sjs-article-font-medium-fontStyle\": \"normal\",\n    \"--sjs-article-font-medium-fontStretch\": \"normal\",\n    \"--sjs-article-font-medium-letterSpacing\": \"0\",\n    \"--sjs-article-font-medium-lineHeight\": \"32px\",\n    \"--sjs-article-font-medium-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-medium-textCase\": \"none\",\n    \"--sjs-article-font-default-textDecoration\": \"none\",\n    \"--sjs-article-font-default-fontWeight\": \"400\",\n    \"--sjs-article-font-default-fontStyle\": \"normal\",\n    \"--sjs-article-font-default-fontStretch\": \"normal\",\n    \"--sjs-article-font-default-letterSpacing\": \"0\",\n    \"--sjs-article-font-default-lineHeight\": \"28px\",\n    \"--sjs-article-font-default-paragraphIndent\": \"0px\",\n    \"--sjs-article-font-default-textCase\": \"none\"\n  }\n};", "import DefaultLight from \"./default-light\";\nimport DefaultDark from \"./default-dark\";\nimport DefaultLightPanelless from \"./default-light-panelless\";\nimport DefaultDarkPanelless from \"./default-dark-panelless\";\nimport SharpLight from \"./sharp-light\";\nimport SharpDark from \"./sharp-dark\";\nimport SharpLightPanelless from \"./sharp-light-panelless\";\nimport SharpDarkPanelless from \"./sharp-dark-panelless\";\nimport BorderlessLight from \"./borderless-light\";\nimport BorderlessDark from \"./borderless-dark\";\nimport BorderlessLightPanelless from \"./borderless-light-panelless\";\nimport BorderlessDarkPanelless from \"./borderless-dark-panelless\";\nimport FlatLight from \"./flat-light\";\nimport FlatDark from \"./flat-dark\";\nimport FlatLightPanelless from \"./flat-light-panelless\";\nimport FlatDarkPanelless from \"./flat-dark-panelless\";\nimport PlainLight from \"./plain-light\";\nimport PlainDark from \"./plain-dark\";\nimport PlainLightPanelless from \"./plain-light-panelless\";\nimport PlainDarkPanelless from \"./plain-dark-panelless\";\nimport DoubleBorderLight from \"./doubleborder-light\";\nimport DoubleBorderDark from \"./doubleborder-dark\";\nimport DoubleBorderLightPanelless from \"./doubleborder-light-panelless\";\nimport DoubleBorderDarkPanelless from \"./doubleborder-dark-panelless\";\nimport LayeredLight from \"./layered-light\";\nimport LayeredDark from \"./layered-dark\";\nimport LayeredLightPanelless from \"./layered-light-panelless\";\nimport LayeredDarkPanelless from \"./layered-dark-panelless\";\nimport SolidLight from \"./solid-light\";\nimport SolidDark from \"./solid-dark\";\nimport SolidLightPanelless from \"./solid-light-panelless\";\nimport SolidDarkPanelless from \"./solid-dark-panelless\";\nimport ThreeDimensionalLight from \"./threedimensional-light\";\nimport ThreeDimensionalDark from \"./threedimensional-dark\";\nimport ThreeDimensionalLightPanelless from \"./threedimensional-light-panelless\";\nimport ThreeDimensionalDarkPanelless from \"./threedimensional-dark-panelless\";\nimport ContrastLight from \"./contrast-light\";\nimport ContrastDark from \"./contrast-dark\";\nimport ContrastLightPanelless from \"./contrast-light-panelless\";\nimport ContrastDarkPanelless from \"./contrast-dark-panelless\";\nconst __surveyjs_internal_themes_hash = true;\nexport { DefaultLight, DefaultDark, DefaultLightPanelless, DefaultDarkPanelless, SharpLight, SharpDark, SharpLightPanelless, SharpDarkPanelless, BorderlessLight, BorderlessDark, BorderlessLightPanelless, BorderlessDarkPanelless, FlatLight, FlatDark, FlatLightPanelless, FlatDarkPanelless, PlainLight, PlainDark, PlainLightPanelless, PlainDarkPanelless, DoubleBorderLight, DoubleBorderDark, DoubleBorderLightPanelless, DoubleBorderDarkPanelless, LayeredLight, LayeredDark, LayeredLightPanelless, LayeredDarkPanelless, SolidLight, SolidDark, SolidLightPanelless, SolidDarkPanelless, ThreeDimensionalLight, ThreeDimensionalDark, ThreeDimensionalLightPanelless, ThreeDimensionalDarkPanelless, ContrastLight, ContrastDark, ContrastLightPanelless, ContrastDarkPanelless, __surveyjs_internal_themes_hash };\nexport default { DefaultLight, DefaultDark, DefaultLightPanelless, DefaultDarkPanelless, SharpLight, SharpDark, SharpLightPanelless, SharpDarkPanelless, BorderlessLight, BorderlessDark, BorderlessLightPanelless, BorderlessDarkPanelless, FlatLight, FlatDark, FlatLightPanelless, FlatDarkPanelless, PlainLight, PlainDark, PlainLightPanelless, PlainDarkPanelless, DoubleBorderLight, DoubleBorderDark, DoubleBorderLightPanelless, DoubleBorderDarkPanelless, LayeredLight, LayeredDark, LayeredLightPanelless, LayeredDarkPanelless, SolidLight, SolidDark, SolidLightPanelless, SolidDarkPanelless, ThreeDimensionalLight, ThreeDimensionalDark, ThreeDimensionalLightPanelless, ThreeDimensionalDarkPanelless, ContrastLight, ContrastDark, ContrastLightPanelless, ContrastDarkPanelless, __surveyjs_internal_themes_hash };"], "mappings": ";;;AAAA,IAAA,eAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,cAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,wBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,uBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,aAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,YAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,sBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,qBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,kBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,iBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,2BAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,0BAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,YAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,WAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,qBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,oBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,aAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,YAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,sBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,qBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,oBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,mBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,6BAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,4BAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,eAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,cAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,wBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,uBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,aAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,YAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,sBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,qBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,wBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,uBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,iCAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,gCAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,gBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,eAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,yBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;ACvFH,IAAA,wBAAe;EACb,aAAa;EACb,gBAAgB;EAChB,eAAe;EACf,gBAAgB;IACd,2BAA2B;IAC3B,gCAAgC;IAChC,+BAA+B;IAC/B,qCAAqC;IACrC,oCAAoC;IACpC,2BAA2B;IAC3B,iCAAiC;IACjC,+BAA+B;IAC/B,qCAAqC;IACrC,2BAA2B;IAC3B,iCAAiC;IACjC,gCAAgC;IAChC,2BAA2B;IAC3B,iCAAiC;IACjC,mBAAmB;IACnB,uBAAuB;IACvB,6BAA6B;IAC7B,mCAAmC;IACnC,wCAAwC;IACxC,6BAA6B;IAC7B,mCAAmC;IACnC,sBAAsB;IACtB,4BAA4B;IAC5B,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,4BAA4B;IAC5B,sBAAsB;IACtB,wBAAwB;IACxB,uBAAuB;IACvB,qBAAqB;IACrB,2BAA2B;IAC3B,+BAA+B;IAC/B,uBAAuB;IACvB,6BAA6B;IAC7B,iCAAiC;IACjC,sBAAsB;IACtB,4BAA4B;IAC5B,gCAAgC;IAChC,wBAAwB;IACxB,8BAA8B;IAC9B,kCAAkC;IAClC,8CAA8C;IAC9C,0CAA0C;IAC1C,yCAAyC;IACzC,2CAA2C;IAC3C,6CAA6C;IAC7C,0CAA0C;IAC1C,+CAA+C;IAC/C,wCAAwC;IACxC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;IACvC,sCAAsC;IACtC,wCAAwC;IACxC,0CAA0C;IAC1C,uCAAuC;IACvC,4CAA4C;IAC5C,qCAAqC;IACrC,4CAA4C;IAC5C,wCAAwC;IACxC,uCAAuC;IACvC,yCAAyC;IACzC,2CAA2C;IAC3C,wCAAwC;IACxC,6CAA6C;IAC7C,sCAAsC;IACtC,6CAA6C;IAC7C,yCAAyC;IACzC,wCAAwC;IACxC,0CAA0C;IAC1C,4CAA4C;IAC5C,yCAAyC;IACzC,8CAA8C;IAC9C,uCAAuC;EACxC;;AC/CH,IAAM,kCAAkC;AAExC,IAAA,QAAe,EAAE,cAAc,aAAa,uBAAuB,sBAAsB,YAAY,WAAW,qBAAqB,oBAAoB,iBAAiB,gBAAgB,0BAA0B,yBAAyB,WAAW,UAAU,oBAAoB,mBAAmB,YAAY,WAAW,qBAAqB,oBAAoB,mBAAmB,kBAAkB,4BAA4B,2BAA2B,cAAc,aAAa,uBAAuB,sBAAsB,YAAY,WAAW,qBAAqB,oBAAoB,uBAAuB,sBAAsB,gCAAgC,+BAA+B,eAAe,cAAc,wBAAwB,uBAAuB,gCAA+B;", "names": []}