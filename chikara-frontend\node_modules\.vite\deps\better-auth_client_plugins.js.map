{"version": 3, "sources": ["../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/plugins/access/index.mjs", "../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/plugins/organization/access/index.mjs", "../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/shared/better-auth.Dt0CvI2z.mjs", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/toPublicKeyCredentialDescriptor.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/isValidDomain.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/identifyRegistrationError.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/toAuthenticatorAttachment.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/methods/startRegistration.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthnAutofill.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/helpers/identifyAuthenticationError.js", "../../../../node_modules/.bun/@simplewebauthn+browser@13.1.2/node_modules/@simplewebauthn/browser/esm/methods/startAuthentication.js", "../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/shared/better-auth.Ddw8bVyV.mjs", "../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/plugins/admin/access/index.mjs", "../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/shared/better-auth.bkwPl2G4.mjs", "../../../../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/client/plugins/index.mjs"], "sourcesContent": ["import { B as BetterAuthError } from '../../shared/better-auth.DdzSJf-n.mjs';\n\nfunction role(statements) {\n  return {\n    authorize(request, connector = \"AND\") {\n      let success = false;\n      for (const [requestedResource, requestedActions] of Object.entries(\n        request\n      )) {\n        const allowedActions = statements[requestedResource];\n        if (!allowedActions) {\n          return {\n            success: false,\n            error: `You are not allowed to access resource: ${requestedResource}`\n          };\n        }\n        if (Array.isArray(requestedActions)) {\n          success = requestedActions.every(\n            (requestedAction) => allowedActions.includes(requestedAction)\n          );\n        } else {\n          if (typeof requestedActions === \"object\") {\n            const actions = requestedActions;\n            if (actions.connector === \"OR\") {\n              success = actions.actions.some(\n                (requestedAction) => allowedActions.includes(requestedAction)\n              );\n            } else {\n              success = actions.actions.every(\n                (requestedAction) => allowedActions.includes(requestedAction)\n              );\n            }\n          } else {\n            throw new BetterAuthError(\"Invalid access control request\");\n          }\n        }\n        if (success && connector === \"OR\") {\n          return { success };\n        }\n        if (!success && connector === \"AND\") {\n          return {\n            success: false,\n            error: `unauthorized to access resource \"${requestedResource}\"`\n          };\n        }\n      }\n      if (success) {\n        return {\n          success\n        };\n      }\n      return {\n        success: false,\n        error: \"Not authorized\"\n      };\n    },\n    statements\n  };\n}\nfunction createAccessControl(s) {\n  return {\n    newRole(statements) {\n      return role(statements);\n    },\n    statements: s\n  };\n}\n\nexport { createAccessControl, role };\n", "import { createAccessControl } from '../../access/index.mjs';\nimport '../../../shared/better-auth.DdzSJf-n.mjs';\n\nconst defaultStatements = {\n  organization: [\"update\", \"delete\"],\n  member: [\"create\", \"update\", \"delete\"],\n  invitation: [\"create\", \"cancel\"],\n  team: [\"create\", \"update\", \"delete\"]\n};\nconst defaultAc = createAccessControl(defaultStatements);\nconst adminAc = defaultAc.newRole({\n  organization: [\"update\"],\n  invitation: [\"create\", \"cancel\"],\n  member: [\"create\", \"update\", \"delete\"],\n  team: [\"create\", \"update\", \"delete\"]\n});\nconst ownerAc = defaultAc.newRole({\n  organization: [\"update\", \"delete\"],\n  member: [\"create\", \"update\", \"delete\"],\n  invitation: [\"create\", \"cancel\"],\n  team: [\"create\", \"update\", \"delete\"]\n});\nconst memberAc = defaultAc.newRole({\n  organization: [],\n  member: [],\n  invitation: [],\n  team: []\n});\nconst defaultRoles = {\n  admin: adminAc,\n  owner: ownerAc,\n  member: memberAc\n};\n\nexport { adminAc, defaultAc, defaultRoles, defaultStatements, memberAc, ownerAc };\n", "import { defaultRoles } from '../plugins/organization/access/index.mjs';\n\nconst hasPermission = (input) => {\n  if (!input.permissions && !input.permission) {\n    return false;\n  }\n  const roles = input.role.split(\",\");\n  const acRoles = input.options.roles || defaultRoles;\n  const creatorRole = input.options.creatorRole || \"owner\";\n  const isCreator = roles.includes(creatorRole);\n  const allowCreatorsAllPermissions = input.allowCreatorAllPermissions || false;\n  if (isCreator && allowCreatorsAllPermissions) return true;\n  for (const role of roles) {\n    const _role = acRoles[role];\n    const result = _role?.authorize(input.permissions ?? input.permission);\n    if (result?.success) {\n      return true;\n    }\n  }\n  return false;\n};\n\nexport { hasPermission as h };\n", "/**\n * Convert the given array buffer into a Base64URL-encoded string. Ideal for converting various\n * credential response ArrayBuffers to string for sending back to the server as JSON.\n *\n * Helper method to compliment `base64URLStringToBuffer`\n */\nexport function bufferToBase64URLString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let str = '';\n    for (const charCode of bytes) {\n        str += String.fromCharCode(charCode);\n    }\n    const base64String = btoa(str);\n    return base64String.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n", "/**\n * Convert from a Base64URL-encoded string to an Array Buffer. Best used when converting a\n * credential ID from a JSON string to an ArrayBuffer, like in allowCredentials or\n * excludeCredentials\n *\n * Helper method to compliment `bufferToBase64URLString`\n */\nexport function base64URLStringToBuffer(base64URLString) {\n    // Convert from Base64URL to Base64\n    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');\n    /**\n     * Pad with '=' until it's a multiple of four\n     * (4 - (85 % 4 = 1) = 3) % 4 = 3 padding\n     * (4 - (86 % 4 = 2) = 2) % 4 = 2 padding\n     * (4 - (87 % 4 = 3) = 1) % 4 = 1 padding\n     * (4 - (88 % 4 = 0) = 4) % 4 = 0 padding\n     */\n    const padLength = (4 - (base64.length % 4)) % 4;\n    const padded = base64.padEnd(base64.length + padLength, '=');\n    // Convert to a binary string\n    const binary = atob(padded);\n    // Convert binary string to buffer\n    const buffer = new ArrayBuffer(binary.length);\n    const bytes = new Uint8Array(buffer);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return buffer;\n}\n", "/**\n * Determine if the browser is capable of Webauthn\n */\nexport function browserSupportsWebAuthn() {\n    return _browserSupportsWebAuthnInternals.stubThis(globalThis?.PublicKeyCredential !== undefined &&\n        typeof globalThis.PublicKeyCredential === 'function');\n}\n/**\n * Make it possible to stub the return value during testing\n * @ignore Don't include this in docs output\n */\nexport const _browserSupportsWebAuthnInternals = {\n    stubThis: (value) => value,\n};\n", "import { base64URLStringToBuffer } from './base64URLStringToBuffer.js';\nexport function toPublicKeyCredentialDescriptor(descriptor) {\n    const { id } = descriptor;\n    return {\n        ...descriptor,\n        id: base64URLStringToBuffer(id),\n        /**\n         * `descriptor.transports` is an array of our `AuthenticatorTransportFuture` that includes newer\n         * transports that TypeScript's DOM lib is ignorant of. Convince TS that our list of transports\n         * are fine to pass to WebAuthn since browsers will recognize the new value.\n         */\n        transports: descriptor.transports,\n    };\n}\n", "/**\n * A simple test to determine if a hostname is a properly-formatted domain name\n *\n * A \"valid domain\" is defined here: https://url.spec.whatwg.org/#valid-domain\n *\n * Regex sourced from here:\n * https://www.oreilly.com/library/view/regular-expressions-cookbook/9781449327453/ch08s15.html\n */\nexport function isValidDomain(hostname) {\n    return (\n    // Consider localhost valid as well since it's okay wrt Secure Contexts\n    hostname === 'localhost' ||\n        /^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$/i.test(hostname));\n}\n", "/**\n * A custom Error used to return a more nuanced error detailing _why_ one of the eight documented\n * errors in the spec was raised after calling `navigator.credentials.create()` or\n * `navigator.credentials.get()`:\n *\n * - `AbortError`\n * - `ConstraintError`\n * - `InvalidStateError`\n * - `NotAllowedError`\n * - `NotSupportedError`\n * - `SecurityError`\n * - `TypeError`\n * - `UnknownError`\n *\n * Error messages were determined through investigation of the spec to determine under which\n * scenarios a given error would be raised.\n */\nexport class WebAuthnError extends Error {\n    constructor({ message, code, cause, name, }) {\n        // @ts-ignore: help <PERSON><PERSON> understand that `cause` is okay to set\n        super(message, { cause });\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = name ?? cause.name;\n        this.code = code;\n    }\n}\n", "import { isValidDomain } from './isValidDomain.js';\nimport { WebAuthnError } from './webAuthnError.js';\n/**\n * Attempt to intuit _why_ an error was raised after calling `navigator.credentials.create()`\n */\nexport function identifyRegistrationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 16)\n            return new WebAuthnError({\n                message: 'Registration ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'ConstraintError') {\n        if (publicKey.authenticatorSelection?.requireResidentKey === true) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 4)\n            return new WebAuthnError({\n                message: 'Discoverable credentials were required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT',\n                cause: error,\n            });\n        }\n        else if (\n        // @ts-ignore: `mediation` doesn't yet exist on CredentialCreationOptions but it's possible as of Sept 2024\n        options.mediation === 'conditional' &&\n            publicKey.authenticatorSelection?.userVerification === 'required') {\n            // https://w3c.github.io/webauthn/#sctn-createCredential (Step 22.4)\n            return new WebAuthnError({\n                message: 'User verification was required during automatic registration but it could not be performed',\n                code: 'ERROR_AUTO_REGISTER_USER_VERIFICATION_FAILURE',\n                cause: error,\n            });\n        }\n        else if (publicKey.authenticatorSelection?.userVerification === 'required') {\n            // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 5)\n            return new WebAuthnError({\n                message: 'User verification was required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'InvalidStateError') {\n        // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 20)\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 3)\n        return new WebAuthnError({\n            message: 'The authenticator was previously registered',\n            code: 'ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotAllowedError') {\n        /**\n         * Pass the error directly through. Platforms are overloading this error beyond what the spec\n         * defines and we don't want to overwrite potentially useful error messages.\n         */\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotSupportedError') {\n        const validPubKeyCredParams = publicKey.pubKeyCredParams.filter((param) => param.type === 'public-key');\n        if (validPubKeyCredParams.length === 0) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 10)\n            return new WebAuthnError({\n                message: 'No entry in pubKeyCredParams was of type \"public-key\"',\n                code: 'ERROR_MALFORMED_PUBKEYCREDPARAMS',\n                cause: error,\n            });\n        }\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 2)\n        return new WebAuthnError({\n            message: 'No available authenticator supported any of the specified pubKeyCredParams algorithms',\n            code: 'ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = globalThis.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 7)\n            return new WebAuthnError({\n                message: `${globalThis.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rp.id !== effectiveDomain) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 8)\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rp.id}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'TypeError') {\n        if (publicKey.user.id.byteLength < 1 || publicKey.user.id.byteLength > 64) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 5)\n            return new WebAuthnError({\n                message: 'User ID was not between 1 and 64 characters',\n                code: 'ERROR_INVALID_USER_ID_LENGTH',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 1)\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 8)\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new credential',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n", "class BaseWebAuthnAbortService {\n    constructor() {\n        Object.defineProperty(this, \"controller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n    }\n    createNewAbortSignal() {\n        // Abort any existing calls to navigator.credentials.create() or navigator.credentials.get()\n        if (this.controller) {\n            const abortError = new Error('Cancelling existing WebAuthn API call for new one');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n        }\n        const newController = new AbortController();\n        this.controller = newController;\n        return newController.signal;\n    }\n    cancelCeremony() {\n        if (this.controller) {\n            const abortError = new Error('Manually cancelling existing WebAuthn API call');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n            this.controller = undefined;\n        }\n    }\n}\n/**\n * A service singleton to help ensure that only a single WebAuthn ceremony is active at a time.\n *\n * Users of **@simplewebauthn/browser** shouldn't typically need to use this, but it can help e.g.\n * developers building projects that use client-side routing to better control the behavior of\n * their UX in response to router navigation events.\n */\nexport const WebAuthnAbortService = new BaseWebAuthnAbortService();\n", "const attachments = ['cross-platform', 'platform'];\n/**\n * If possible coerce a `string` value into a known `AuthenticatorAttachment`\n */\nexport function toAuthenticatorAttachment(attachment) {\n    if (!attachment) {\n        return;\n    }\n    if (attachments.indexOf(attachment) < 0) {\n        return;\n    }\n    return attachment;\n}\n", "import { bufferToBase64URLString } from '../helpers/bufferToBase64URLString.js';\nimport { base64URLStringToBuffer } from '../helpers/base64URLStringToBuffer.js';\nimport { browserSupportsWebAuthn } from '../helpers/browserSupportsWebAuthn.js';\nimport { toPublicKeyCredentialDescriptor } from '../helpers/toPublicKeyCredentialDescriptor.js';\nimport { identifyRegistrationError } from '../helpers/identifyRegistrationError.js';\nimport { WebAuthnAbortService } from '../helpers/webAuthnAbortService.js';\nimport { toAuthenticatorAttachment } from '../helpers/toAuthenticatorAttachment.js';\n/**\n * Begin authenticator \"registration\" via WebAuthn attestation\n *\n * @param optionsJSON Output from **@simplewebauthn/server**'s `generateRegistrationOptions()`\n * @param useAutoRegister (Optional) Try to silently create a passkey with the password manager that the user just signed in with. Defaults to `false`.\n */\nexport async function startRegistration(options) {\n    // @ts-ignore: Intentionally check for old call structure to warn about improper API call\n    if (!options.optionsJSON && options.challenge) {\n        console.warn('startRegistration() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information.');\n        // @ts-ignore: Reassign the options, passed in as a positional argument, to the expected variable\n        options = { optionsJSON: options };\n    }\n    const { optionsJSON, useAutoRegister = false } = options;\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    // We need to convert some values to Uint8Arrays before passing the credentials to the navigator\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        user: {\n            ...optionsJSON.user,\n            id: base64URLStringToBuffer(optionsJSON.user.id),\n        },\n        excludeCredentials: optionsJSON.excludeCredentials?.map(toPublicKeyCredentialDescriptor),\n    };\n    // Prepare options for `.create()`\n    const createOptions = {};\n    /**\n     * Try to use conditional create to register a passkey for the user with the password manager\n     * the user just used to authenticate with. The user won't be shown any prominent UI by the\n     * browser.\n     */\n    if (useAutoRegister) {\n        // @ts-ignore: `mediation` doesn't yet exist on CredentialCreationOptions but it's possible as of Sept 2024\n        createOptions.mediation = 'conditional';\n    }\n    // Finalize options\n    createOptions.publicKey = publicKey;\n    // Set up the ability to cancel this request if the user attempts another\n    createOptions.signal = WebAuthnAbortService.createNewAbortSignal();\n    // Wait for the user to complete attestation\n    let credential;\n    try {\n        credential = (await navigator.credentials.create(createOptions));\n    }\n    catch (err) {\n        throw identifyRegistrationError({ error: err, options: createOptions });\n    }\n    if (!credential) {\n        throw new Error('Registration was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    // Continue to play it safe with `getTransports()` for now, even when L3 types say it's required\n    let transports = undefined;\n    if (typeof response.getTransports === 'function') {\n        transports = response.getTransports();\n    }\n    // L3 says this is required, but browser and webview support are still not guaranteed.\n    let responsePublicKeyAlgorithm = undefined;\n    if (typeof response.getPublicKeyAlgorithm === 'function') {\n        try {\n            responsePublicKeyAlgorithm = response.getPublicKeyAlgorithm();\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKeyAlgorithm()', error);\n        }\n    }\n    let responsePublicKey = undefined;\n    if (typeof response.getPublicKey === 'function') {\n        try {\n            const _publicKey = response.getPublicKey();\n            if (_publicKey !== null) {\n                responsePublicKey = bufferToBase64URLString(_publicKey);\n            }\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKey()', error);\n        }\n    }\n    // L3 says this is required, but browser and webview support are still not guaranteed.\n    let responseAuthenticatorData;\n    if (typeof response.getAuthenticatorData === 'function') {\n        try {\n            responseAuthenticatorData = bufferToBase64URLString(response.getAuthenticatorData());\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getAuthenticatorData()', error);\n        }\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            attestationObject: bufferToBase64URLString(response.attestationObject),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            transports,\n            publicKeyAlgorithm: responsePublicKeyAlgorithm,\n            publicKey: responsePublicKey,\n            authenticatorData: responseAuthenticatorData,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\n/**\n * Visibly warn when we detect an issue related to a passkey provider intercepting WebAuthn API\n * calls\n */\nfunction warnOnBrokenImplementation(methodName, cause) {\n    console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${methodName}. You should report this error to them.\\n`, cause);\n}\n", "import { browserSupportsWebAuthn } from './browserSupportsWebAuthn.js';\n/**\n * Determine if the browser supports conditional UI, so that WebAuthn credentials can\n * be shown to the user in the browser's typical password autofill popup.\n */\nexport function browserSupportsWebAuthnAutofill() {\n    if (!browserSupportsWebAuthn()) {\n        return _browserSupportsWebAuthnAutofillInternals.stubThis(new Promise((resolve) => resolve(false)));\n    }\n    /**\n     * I don't like the `as unknown` here but there's a `declare var PublicKeyCredential` in\n     * TS' DOM lib that's making it difficult for me to just go `as PublicKeyCredentialFuture` as I\n     * want. I think I'm fine with this for now since it's _supposed_ to be temporary, until TS types\n     * have a chance to catch up.\n     */\n    const globalPublicKeyCredential = globalThis\n        .PublicKeyCredential;\n    if (globalPublicKeyCredential?.isConditionalMediationAvailable === undefined) {\n        return _browserSupportsWebAuthnAutofillInternals.stubThis(new Promise((resolve) => resolve(false)));\n    }\n    return _browserSupportsWebAuthnAutofillInternals.stubThis(globalPublicKeyCredential.isConditionalMediationAvailable());\n}\n// Make it possible to stub the return value during testing\nexport const _browserSupportsWebAuthnAutofillInternals = {\n    stubThis: (value) => value,\n};\n", "import { isValidDomain } from './isValidDomain.js';\nimport { WebAuthnError } from './webAuthnError.js';\n/**\n * Attempt to intuit _why_ an error was raised after calling `navigator.credentials.get()`\n */\nexport function identifyAuthenticationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 16)\n            return new WebAuthnError({\n                message: 'Authentication ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'NotAllowedError') {\n        /**\n         * Pass the error directly through. Platforms are overloading this error beyond what the spec\n         * defines and we don't want to overwrite potentially useful error messages.\n         */\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = globalThis.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-discover-from-external-source (Step 5)\n            return new WebAuthnError({\n                message: `${globalThis.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rpId !== effectiveDomain) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-discover-from-external-source (Step 6)\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rpId}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-get-assertion (Step 1)\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-get-assertion (Step 12)\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new assertion signature',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n", "import { bufferToBase64URLString } from '../helpers/bufferToBase64URLString.js';\nimport { base64URLStringToBuffer } from '../helpers/base64URLStringToBuffer.js';\nimport { browserSupportsWebAuthn } from '../helpers/browserSupportsWebAuthn.js';\nimport { browserSupportsWebAuthnAutofill } from '../helpers/browserSupportsWebAuthnAutofill.js';\nimport { toPublicKeyCredentialDescriptor } from '../helpers/toPublicKeyCredentialDescriptor.js';\nimport { identifyAuthenticationError } from '../helpers/identifyAuthenticationError.js';\nimport { WebAuthnAbortService } from '../helpers/webAuthnAbortService.js';\nimport { toAuthenticatorAttachment } from '../helpers/toAuthenticatorAttachment.js';\n/**\n * Begin authenticator \"login\" via WebAuthn assertion\n *\n * @param optionsJSON Output from **@simplewebauthn/server**'s `generateAuthenticationOptions()`\n * @param useBrowserAutofill (Optional) Initialize conditional UI to enable logging in via browser autofill prompts. Defaults to `false`.\n * @param verifyBrowserAutofillInput (Optional) Ensure a suitable `<input>` element is present when `useBrowserAutofill` is `true`. Defaults to `true`.\n */\nexport async function startAuthentication(options) {\n    // @ts-ignore: Intentionally check for old call structure to warn about improper API call\n    if (!options.optionsJSON && options.challenge) {\n        console.warn('startAuthentication() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information.');\n        // @ts-ignore: Reassign the options, passed in as a positional argument, to the expected variable\n        options = { optionsJSON: options };\n    }\n    const { optionsJSON, useBrowserAutofill = false, verifyBrowserAutofillInput = true, } = options;\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    // We need to avoid passing empty array to avoid blocking retrieval\n    // of public key\n    let allowCredentials;\n    if (optionsJSON.allowCredentials?.length !== 0) {\n        allowCredentials = optionsJSON.allowCredentials?.map(toPublicKeyCredentialDescriptor);\n    }\n    // We need to convert some values to Uint8Arrays before passing the credentials to the navigator\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        allowCredentials,\n    };\n    // Prepare options for `.get()`\n    const getOptions = {};\n    /**\n     * Set up the page to prompt the user to select a credential for authentication via the browser's\n     * input autofill mechanism.\n     */\n    if (useBrowserAutofill) {\n        if (!(await browserSupportsWebAuthnAutofill())) {\n            throw Error('Browser does not support WebAuthn autofill');\n        }\n        // Check for an <input> with \"webauthn\" in its `autocomplete` attribute\n        const eligibleInputs = document.querySelectorAll(\"input[autocomplete$='webauthn']\");\n        // WebAuthn autofill requires at least one valid input\n        if (eligibleInputs.length < 1 && verifyBrowserAutofillInput) {\n            throw Error('No <input> with \"webauthn\" as the only or last value in its `autocomplete` attribute was detected');\n        }\n        // `CredentialMediationRequirement` doesn't know about \"conditional\" yet as of\n        // typescript@4.6.3\n        getOptions.mediation = 'conditional';\n        // Conditional UI requires an empty allow list\n        publicKey.allowCredentials = [];\n    }\n    // Finalize options\n    getOptions.publicKey = publicKey;\n    // Set up the ability to cancel this request if the user attempts another\n    getOptions.signal = WebAuthnAbortService.createNewAbortSignal();\n    // Wait for the user to complete assertion\n    let credential;\n    try {\n        credential = (await navigator.credentials.get(getOptions));\n    }\n    catch (err) {\n        throw identifyAuthenticationError({ error: err, options: getOptions });\n    }\n    if (!credential) {\n        throw new Error('Authentication was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let userHandle = undefined;\n    if (response.userHandle) {\n        userHandle = bufferToBase64URLString(response.userHandle);\n    }\n    // Convert values to base64 to make it easier to send back to the server\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            authenticatorData: bufferToBase64URLString(response.authenticatorData),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            signature: bufferToBase64URLString(response.signature),\n            userHandle,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\n", "const twoFactorClient = (options) => {\n  return {\n    id: \"two-factor\",\n    $InferServerPlugin: {},\n    atomListeners: [\n      {\n        matcher: (path) => path.startsWith(\"/two-factor/\"),\n        signal: \"$sessionSignal\"\n      }\n    ],\n    pathMethods: {\n      \"/two-factor/disable\": \"POST\",\n      \"/two-factor/enable\": \"POST\",\n      \"/two-factor/send-otp\": \"POST\",\n      \"/two-factor/generate-backup-codes\": \"POST\"\n    },\n    fetchPlugins: [\n      {\n        id: \"two-factor\",\n        name: \"two-factor\",\n        hooks: {\n          async onSuccess(context) {\n            if (context.data?.twoFactorRedirect) {\n              if (options?.onTwoFactorRedirect) {\n                await options.onTwoFactorRedirect();\n              }\n            }\n          }\n        }\n      }\n    ]\n  };\n};\n\nexport { twoFactorClient as t };\n", "import { createAccessControl } from '../../access/index.mjs';\nimport '../../../shared/better-auth.DdzSJf-n.mjs';\n\nconst defaultStatements = {\n  user: [\n    \"create\",\n    \"list\",\n    \"set-role\",\n    \"ban\",\n    \"impersonate\",\n    \"delete\",\n    \"set-password\",\n    \"update\"\n  ],\n  session: [\"list\", \"revoke\", \"delete\"]\n};\nconst defaultAc = createAccessControl(defaultStatements);\nconst adminAc = defaultAc.newRole({\n  user: [\n    \"create\",\n    \"list\",\n    \"set-role\",\n    \"ban\",\n    \"impersonate\",\n    \"delete\",\n    \"set-password\",\n    \"update\"\n  ],\n  session: [\"list\", \"revoke\", \"delete\"]\n});\nconst userAc = defaultAc.newRole({\n  user: [],\n  session: []\n});\nconst defaultRoles = {\n  admin: adminAc,\n  user: userAc\n};\n\nexport { adminAc, defaultAc, defaultRoles, defaultStatements, userAc };\n", "import { defaultRoles } from '../plugins/admin/access/index.mjs';\n\nconst hasPermission = (input) => {\n  if (input.userId && input.options?.adminUserIds?.includes(input.userId)) {\n    return true;\n  }\n  if (!input.permissions && !input.permission) {\n    return false;\n  }\n  const roles = (input.role || input.options?.defaultRole || \"user\").split(\",\");\n  const acRoles = input.options?.roles || defaultRoles;\n  for (const role of roles) {\n    const _role = acRoles[role];\n    const result = _role?.authorize(input.permission ?? input.permissions);\n    if (result?.success) {\n      return true;\n    }\n  }\n  return false;\n};\n\nexport { hasPermission as h };\n", "import { atom } from 'nanostores';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.CMQ3rA-I.mjs';\nimport { u as useAuthQuery } from '../../shared/better-auth.Buni1mmI.mjs';\nimport { ownerAc, memberAc, adminAc } from '../../plugins/organization/access/index.mjs';\nimport { h as hasPermission } from '../../shared/better-auth.Dt0CvI2z.mjs';\nimport { startRegistration, WebAuthnError, startAuthentication } from '@simplewebauthn/browser';\nexport { t as twoFactorClient } from '../../shared/better-auth.Ddw8bVyV.mjs';\nimport { userAc, adminAc as adminAc$1 } from '../../plugins/admin/access/index.mjs';\nimport { h as hasPermission$1 } from '../../shared/better-auth.bkwPl2G4.mjs';\nimport '../../plugins/access/index.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\n\nconst organizationClient = (options) => {\n  const $listOrg = atom(false);\n  const $activeOrgSignal = atom(false);\n  const $activeMemberSignal = atom(false);\n  const roles = {\n    admin: adminAc,\n    member: memberAc,\n    owner: ownerAc,\n    ...options?.roles\n  };\n  return {\n    id: \"organization\",\n    $InferServerPlugin: {},\n    getActions: ($fetch) => ({\n      $Infer: {\n        ActiveOrganization: {},\n        Organization: {},\n        Invitation: {},\n        Member: {},\n        Team: {}\n      },\n      organization: {\n        checkRolePermission: (data) => {\n          const isAuthorized = hasPermission({\n            role: data.role,\n            options: {\n              ac: options?.ac,\n              roles\n            },\n            permissions: data.permissions ?? data.permission\n          });\n          return isAuthorized;\n        }\n      }\n    }),\n    getAtoms: ($fetch) => {\n      const listOrganizations = useAuthQuery(\n        $listOrg,\n        \"/organization/list\",\n        $fetch,\n        {\n          method: \"GET\"\n        }\n      );\n      const activeOrganization = useAuthQuery(\n        [$activeOrgSignal],\n        \"/organization/get-full-organization\",\n        $fetch,\n        () => ({\n          method: \"GET\"\n        })\n      );\n      const activeMember = useAuthQuery(\n        [$activeMemberSignal],\n        \"/organization/get-active-member\",\n        $fetch,\n        {\n          method: \"GET\"\n        }\n      );\n      return {\n        $listOrg,\n        $activeOrgSignal,\n        $activeMemberSignal,\n        activeOrganization,\n        listOrganizations,\n        activeMember\n      };\n    },\n    pathMethods: {\n      \"/organization/get-full-organization\": \"GET\"\n    },\n    atomListeners: [\n      {\n        matcher(path) {\n          return path === \"/organization/create\" || path === \"/organization/delete\" || path === \"/organization/update\";\n        },\n        signal: \"$listOrg\"\n      },\n      {\n        matcher(path) {\n          return path.startsWith(\"/organization\");\n        },\n        signal: \"$activeOrgSignal\"\n      },\n      {\n        matcher(path) {\n          return path.startsWith(\"/organization/set-active\");\n        },\n        signal: \"$sessionSignal\"\n      },\n      {\n        matcher(path) {\n          return path.includes(\"/organization/update-member-role\");\n        },\n        signal: \"$activeMemberSignal\"\n      }\n    ]\n  };\n};\nconst inferOrgAdditionalFields = (schema) => {\n  return {};\n};\n\nconst usernameClient = () => {\n  return {\n    id: \"username\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst getPasskeyActions = ($fetch, {\n  $listPasskeys\n}) => {\n  const signInPasskey = async (opts, options) => {\n    const response = await $fetch(\n      \"/passkey/generate-authenticate-options\",\n      {\n        method: \"POST\",\n        body: {\n          email: opts?.email\n        }\n      }\n    );\n    if (!response.data) {\n      return response;\n    }\n    try {\n      const res = await startAuthentication({\n        optionsJSON: response.data,\n        useBrowserAutofill: opts?.autoFill\n      });\n      const verified = await $fetch(\"/passkey/verify-authentication\", {\n        body: {\n          response: res\n        },\n        ...opts?.fetchOptions,\n        ...options,\n        method: \"POST\"\n      });\n      if (!verified.data) {\n        return verified;\n      }\n    } catch (e) {\n      return {\n        data: null,\n        error: {\n          message: \"auth cancelled\",\n          status: 400,\n          statusText: \"BAD_REQUEST\"\n        }\n      };\n    }\n  };\n  const registerPasskey = async (opts, fetchOpts) => {\n    const options = await $fetch(\n      \"/passkey/generate-register-options\",\n      {\n        method: \"GET\",\n        query: {\n          ...opts?.authenticatorAttachment && {\n            authenticatorAttachment: opts.authenticatorAttachment\n          },\n          ...opts?.name && {\n            name: opts.name\n          }\n        }\n      }\n    );\n    if (!options.data) {\n      return options;\n    }\n    try {\n      const res = await startRegistration({\n        optionsJSON: options.data,\n        useAutoRegister: opts?.useAutoRegister\n      });\n      const verified = await $fetch(\"/passkey/verify-registration\", {\n        ...opts?.fetchOptions,\n        ...fetchOpts,\n        body: {\n          response: res,\n          name: opts?.name\n        },\n        method: \"POST\"\n      });\n      if (!verified.data) {\n        return verified;\n      }\n      $listPasskeys.set(Math.random());\n    } catch (e) {\n      if (e instanceof WebAuthnError) {\n        if (e.code === \"ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED\") {\n          return {\n            data: null,\n            error: {\n              message: \"previously registered\",\n              status: 400,\n              statusText: \"BAD_REQUEST\"\n            }\n          };\n        }\n        if (e.code === \"ERROR_CEREMONY_ABORTED\") {\n          return {\n            data: null,\n            error: {\n              message: \"registration cancelled\",\n              status: 400,\n              statusText: \"BAD_REQUEST\"\n            }\n          };\n        }\n        return {\n          data: null,\n          error: {\n            message: e.message,\n            status: 400,\n            statusText: \"BAD_REQUEST\"\n          }\n        };\n      }\n      return {\n        data: null,\n        error: {\n          message: e instanceof Error ? e.message : \"unknown error\",\n          status: 500,\n          statusText: \"INTERNAL_SERVER_ERROR\"\n        }\n      };\n    }\n  };\n  return {\n    signIn: {\n      /**\n       * Sign in with a registered passkey\n       */\n      passkey: signInPasskey\n    },\n    passkey: {\n      /**\n       * Add a passkey to the user account\n       */\n      addPasskey: registerPasskey\n    },\n    /**\n     * Inferred Internal Types\n     */\n    $Infer: {}\n  };\n};\nconst passkeyClient = () => {\n  const $listPasskeys = atom();\n  return {\n    id: \"passkey\",\n    $InferServerPlugin: {},\n    getActions: ($fetch) => getPasskeyActions($fetch, {\n      $listPasskeys\n    }),\n    getAtoms($fetch) {\n      const listPasskeys = useAuthQuery(\n        $listPasskeys,\n        \"/passkey/list-user-passkeys\",\n        $fetch,\n        {\n          method: \"GET\"\n        }\n      );\n      return {\n        listPasskeys,\n        $listPasskeys\n      };\n    },\n    pathMethods: {\n      \"/passkey/register\": \"POST\",\n      \"/passkey/authenticate\": \"POST\"\n    },\n    atomListeners: [\n      {\n        matcher(path) {\n          return path === \"/passkey/verify-registration\" || path === \"/passkey/delete-passkey\" || path === \"/passkey/update-passkey\";\n        },\n        signal: \"_listPasskeys\"\n      }\n    ]\n  };\n};\n\nconst magicLinkClient = () => {\n  return {\n    id: \"magic-link\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst phoneNumberClient = () => {\n  return {\n    id: \"phoneNumber\",\n    $InferServerPlugin: {},\n    atomListeners: [\n      {\n        matcher(path) {\n          return path === \"/phone-number/update\" || path === \"/phone-number/verify\";\n        },\n        signal: \"$sessionSignal\"\n      }\n    ]\n  };\n};\n\nconst anonymousClient = () => {\n  return {\n    id: \"anonymous\",\n    $InferServerPlugin: {},\n    pathMethods: {\n      \"/sign-in/anonymous\": \"POST\"\n    }\n  };\n};\n\nconst inferAdditionalFields = (schema) => {\n  return {\n    id: \"additional-fields-client\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst adminClient = (options) => {\n  const roles = {\n    admin: adminAc$1,\n    user: userAc,\n    ...options?.roles\n  };\n  return {\n    id: \"admin-client\",\n    $InferServerPlugin: {},\n    getActions: () => ({\n      admin: {\n        checkRolePermission: (data) => {\n          const isAuthorized = hasPermission$1({\n            role: data.role,\n            options: {\n              ac: options?.ac,\n              roles\n            },\n            permissions: data.permissions ?? data.permission\n          });\n          return isAuthorized;\n        }\n      }\n    }),\n    pathMethods: {\n      \"/admin/list-users\": \"GET\",\n      \"/admin/stop-impersonating\": \"POST\"\n    }\n  };\n};\n\nconst genericOAuthClient = () => {\n  return {\n    id: \"generic-oauth-client\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst jwtClient = () => {\n  return {\n    id: \"better-auth-client\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst multiSessionClient = () => {\n  return {\n    id: \"multi-session\",\n    $InferServerPlugin: {},\n    atomListeners: [\n      {\n        matcher(path) {\n          return path === \"/multi-session/set-active\";\n        },\n        signal: \"$sessionSignal\"\n      }\n    ]\n  };\n};\n\nconst emailOTPClient = () => {\n  return {\n    id: \"email-otp\",\n    $InferServerPlugin: {}\n  };\n};\n\nlet isRequestInProgress = false;\nconst oneTapClient = (options) => {\n  return {\n    id: \"one-tap\",\n    getActions: ($fetch, _) => ({\n      oneTap: async (opts, fetchOptions) => {\n        if (isRequestInProgress) {\n          console.warn(\n            \"A Google One Tap request is already in progress. Please wait.\"\n          );\n          return;\n        }\n        isRequestInProgress = true;\n        try {\n          if (typeof window === \"undefined\" || !window.document) {\n            console.warn(\n              \"Google One Tap is only available in browser environments\"\n            );\n            return;\n          }\n          const { autoSelect, cancelOnTapOutside, context } = opts ?? {};\n          const contextValue = context ?? options.context ?? \"signin\";\n          await loadGoogleScript();\n          await new Promise((resolve, reject) => {\n            let isResolved = false;\n            const baseDelay = options.promptOptions?.baseDelay ?? 1e3;\n            const maxAttempts = options.promptOptions?.maxAttempts ?? 5;\n            window.google?.accounts.id.initialize({\n              client_id: options.clientId,\n              callback: async (response) => {\n                isResolved = true;\n                try {\n                  await $fetch(\"/one-tap/callback\", {\n                    method: \"POST\",\n                    body: { idToken: response.credential },\n                    ...opts?.fetchOptions,\n                    ...fetchOptions\n                  });\n                  if (!opts?.fetchOptions && !fetchOptions || opts?.callbackURL) {\n                    window.location.href = opts?.callbackURL ?? \"/\";\n                  }\n                  resolve();\n                } catch (error) {\n                  console.error(\"Error during One Tap callback:\", error);\n                  reject(error);\n                }\n              },\n              auto_select: autoSelect,\n              cancel_on_tap_outside: cancelOnTapOutside,\n              context: contextValue,\n              ...options.additionalOptions\n            });\n            const handlePrompt = (attempt) => {\n              if (isResolved) return;\n              window.google?.accounts.id.prompt((notification) => {\n                if (isResolved) return;\n                if (notification.isDismissedMoment && notification.isDismissedMoment()) {\n                  if (attempt < maxAttempts) {\n                    const delay = Math.pow(2, attempt) * baseDelay;\n                    setTimeout(() => handlePrompt(attempt + 1), delay);\n                  } else {\n                    opts?.onPromptNotification?.(notification);\n                  }\n                } else if (notification.isSkippedMoment && notification.isSkippedMoment()) {\n                  if (attempt < maxAttempts) {\n                    const delay = Math.pow(2, attempt) * baseDelay;\n                    setTimeout(() => handlePrompt(attempt + 1), delay);\n                  } else {\n                    opts?.onPromptNotification?.(notification);\n                  }\n                }\n              });\n            };\n            handlePrompt(0);\n          });\n        } catch (error) {\n          console.error(\"Error during Google One Tap flow:\", error);\n          throw error;\n        } finally {\n          isRequestInProgress = false;\n        }\n      }\n    }),\n    getAtoms($fetch) {\n      return {};\n    }\n  };\n};\nconst loadGoogleScript = () => {\n  return new Promise((resolve) => {\n    if (window.googleScriptInitialized) {\n      resolve();\n      return;\n    }\n    const script = document.createElement(\"script\");\n    script.src = \"https://accounts.google.com/gsi/client\";\n    script.async = true;\n    script.defer = true;\n    script.onload = () => {\n      window.googleScriptInitialized = true;\n      resolve();\n    };\n    document.head.appendChild(script);\n  });\n};\n\nconst customSessionClient = () => {\n  return InferServerPlugin();\n};\n\nconst InferServerPlugin = () => {\n  return {\n    id: \"infer-server-plugin\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst ssoClient = () => {\n  return {\n    id: \"sso-client\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst oidcClient = () => {\n  return {\n    id: \"oidc-client\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst apiKeyClient = () => {\n  return {\n    id: \"api-key\",\n    $InferServerPlugin: {},\n    pathMethods: {\n      \"/api-key/create\": \"POST\",\n      \"/api-key/delete\": \"POST\",\n      \"/api-key/delete-all-expired-api-keys\": \"POST\"\n    }\n  };\n};\n\nconst oneTimeTokenClient = () => {\n  return {\n    id: \"one-time-token\",\n    $InferServerPlugin: {}\n  };\n};\n\nconst siweClient = () => {\n  return {\n    id: \"siwe\",\n    $InferServerPlugin: {}\n  };\n};\n\nexport { InferServerPlugin, adminClient, anonymousClient, apiKeyClient, customSessionClient, emailOTPClient, genericOAuthClient, getPasskeyActions, inferAdditionalFields, inferOrgAdditionalFields, jwtClient, magicLinkClient, multiSessionClient, oidcClient, oneTapClient, oneTimeTokenClient, organizationClient, passkeyClient, phoneNumberClient, siweClient, ssoClient, usernameClient };\n"], "mappings": ";;;;;;;;AAEA,SAAS,KAAK,YAAY;AACxB,SAAO;AAAA,IACL,UAAU,SAAS,YAAY,OAAO;AACpC,UAAI,UAAU;AACd,iBAAW,CAAC,mBAAmB,gBAAgB,KAAK,OAAO;AAAA,QACzD;AAAA,MACF,GAAG;AACD,cAAM,iBAAiB,WAAW,iBAAiB;AACnD,YAAI,CAAC,gBAAgB;AACnB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO,2CAA2C,iBAAiB;AAAA,UACrE;AAAA,QACF;AACA,YAAI,MAAM,QAAQ,gBAAgB,GAAG;AACnC,oBAAU,iBAAiB;AAAA,YACzB,CAAC,oBAAoB,eAAe,SAAS,eAAe;AAAA,UAC9D;AAAA,QACF,OAAO;AACL,cAAI,OAAO,qBAAqB,UAAU;AACxC,kBAAM,UAAU;AAChB,gBAAI,QAAQ,cAAc,MAAM;AAC9B,wBAAU,QAAQ,QAAQ;AAAA,gBACxB,CAAC,oBAAoB,eAAe,SAAS,eAAe;AAAA,cAC9D;AAAA,YACF,OAAO;AACL,wBAAU,QAAQ,QAAQ;AAAA,gBACxB,CAAC,oBAAoB,eAAe,SAAS,eAAe;AAAA,cAC9D;AAAA,YACF;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,gBAAgB,gCAAgC;AAAA,UAC5D;AAAA,QACF;AACA,YAAI,WAAW,cAAc,MAAM;AACjC,iBAAO,EAAE,QAAQ;AAAA,QACnB;AACA,YAAI,CAAC,WAAW,cAAc,OAAO;AACnC,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO,oCAAoC,iBAAiB;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS;AACX,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,GAAG;AAC9B,SAAO;AAAA,IACL,QAAQ,YAAY;AAClB,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,EACd;AACF;;;AC/DA,IAAM,oBAAoB;AAAA,EACxB,cAAc,CAAC,UAAU,QAAQ;AAAA,EACjC,QAAQ,CAAC,UAAU,UAAU,QAAQ;AAAA,EACrC,YAAY,CAAC,UAAU,QAAQ;AAAA,EAC/B,MAAM,CAAC,UAAU,UAAU,QAAQ;AACrC;AACA,IAAM,YAAY,oBAAoB,iBAAiB;AACvD,IAAM,UAAU,UAAU,QAAQ;AAAA,EAChC,cAAc,CAAC,QAAQ;AAAA,EACvB,YAAY,CAAC,UAAU,QAAQ;AAAA,EAC/B,QAAQ,CAAC,UAAU,UAAU,QAAQ;AAAA,EACrC,MAAM,CAAC,UAAU,UAAU,QAAQ;AACrC,CAAC;AACD,IAAM,UAAU,UAAU,QAAQ;AAAA,EAChC,cAAc,CAAC,UAAU,QAAQ;AAAA,EACjC,QAAQ,CAAC,UAAU,UAAU,QAAQ;AAAA,EACrC,YAAY,CAAC,UAAU,QAAQ;AAAA,EAC/B,MAAM,CAAC,UAAU,UAAU,QAAQ;AACrC,CAAC;AACD,IAAM,WAAW,UAAU,QAAQ;AAAA,EACjC,cAAc,CAAC;AAAA,EACf,QAAQ,CAAC;AAAA,EACT,YAAY,CAAC;AAAA,EACb,MAAM,CAAC;AACT,CAAC;AACD,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;;;AC9BA,IAAM,gBAAgB,CAAC,UAAU;AAC/B,MAAI,CAAC,MAAM,eAAe,CAAC,MAAM,YAAY;AAC3C,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,KAAK,MAAM,GAAG;AAClC,QAAM,UAAU,MAAM,QAAQ,SAAS;AACvC,QAAM,cAAc,MAAM,QAAQ,eAAe;AACjD,QAAM,YAAY,MAAM,SAAS,WAAW;AAC5C,QAAM,8BAA8B,MAAM,8BAA8B;AACxE,MAAI,aAAa,4BAA6B,QAAO;AACrD,aAAWA,SAAQ,OAAO;AACxB,UAAM,QAAQ,QAAQA,KAAI;AAC1B,UAAM,SAAS,OAAO,UAAU,MAAM,eAAe,MAAM,UAAU;AACrE,QAAI,QAAQ,SAAS;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACdO,SAAS,wBAAwB,QAAQ;AAC5C,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,MAAI,MAAM;AACV,aAAW,YAAY,OAAO;AAC1B,WAAO,OAAO,aAAa,QAAQ;AAAA,EACvC;AACA,QAAM,eAAe,KAAK,GAAG;AAC7B,SAAO,aAAa,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAChF;;;ACPO,SAAS,wBAAwB,iBAAiB;AAErD,QAAM,SAAS,gBAAgB,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAQnE,QAAM,aAAa,IAAK,OAAO,SAAS,KAAM;AAC9C,QAAM,SAAS,OAAO,OAAO,OAAO,SAAS,WAAW,GAAG;AAE3D,QAAM,SAAS,KAAK,MAAM;AAE1B,QAAM,SAAS,IAAI,YAAY,OAAO,MAAM;AAC5C,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAClC;AACA,SAAO;AACX;;;ACzBO,SAAS,0BAA0B;AACtC,SAAO,kCAAkC,SAAS,YAAY,wBAAwB,UAClF,OAAO,WAAW,wBAAwB,UAAU;AAC5D;AAKO,IAAM,oCAAoC;AAAA,EAC7C,UAAU,CAAC,UAAU;AACzB;;;ACZO,SAAS,gCAAgC,YAAY;AACxD,QAAM,EAAE,GAAG,IAAI;AACf,SAAO;AAAA,IACH,GAAG;AAAA,IACH,IAAI,wBAAwB,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAM9B,YAAY,WAAW;AAAA,EAC3B;AACJ;;;ACLO,SAAS,cAAc,UAAU;AACpC;AAAA;AAAA,IAEA,aAAa,eACT,0CAA0C,KAAK,QAAQ;AAAA;AAC/D;;;ACIO,IAAM,gBAAN,cAA4B,MAAM;AAAA,EACrC,YAAY,EAAE,SAAS,MAAM,OAAO,KAAM,GAAG;AAEzC,UAAM,SAAS,EAAE,MAAM,CAAC;AACxB,WAAO,eAAe,MAAM,QAAQ;AAAA,MAChC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AACD,SAAK,OAAO,QAAQ,MAAM;AAC1B,SAAK,OAAO;AAAA,EAChB;AACJ;;;ACzBO,SAAS,0BAA0B,EAAE,OAAO,QAAS,GAAG;AAC3D,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,CAAC,WAAW;AACZ,UAAM,MAAM,iDAAiD;AAAA,EACjE;AACA,MAAI,MAAM,SAAS,cAAc;AAC7B,QAAI,QAAQ,kBAAkB,aAAa;AAEvC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,mBAAmB;AACvC,QAAI,UAAU,wBAAwB,uBAAuB,MAAM;AAE/D,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA;AAAA,MAGA,QAAQ,cAAc,iBAClB,UAAU,wBAAwB,qBAAqB;AAAA,MAAY;AAEnE,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,wBAAwB,qBAAqB,YAAY;AAExE,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,qBAAqB;AAGzC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,mBAAmB;AAKvC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS,MAAM;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,qBAAqB;AACzC,UAAM,wBAAwB,UAAU,iBAAiB,OAAO,CAAC,UAAU,MAAM,SAAS,YAAY;AACtG,QAAI,sBAAsB,WAAW,GAAG;AAEpC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAEA,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,iBAAiB;AACrC,UAAM,kBAAkB,WAAW,SAAS;AAC5C,QAAI,CAAC,cAAc,eAAe,GAAG;AAEjC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,GAAG,WAAW,SAAS,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,GAAG,OAAO,iBAAiB;AAE1C,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,cAAc,UAAU,GAAG,EAAE;AAAA,QACtC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,aAAa;AACjC,QAAI,UAAU,KAAK,GAAG,aAAa,KAAK,UAAU,KAAK,GAAG,aAAa,IAAI;AAEvE,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,gBAAgB;AAGpC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAO;AACX;;;AC7HA,IAAM,2BAAN,MAA+B;AAAA,EAC3B,cAAc;AACV,WAAO,eAAe,MAAM,cAAc;AAAA,MACtC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA,EACA,uBAAuB;AAEnB,QAAI,KAAK,YAAY;AACjB,YAAM,aAAa,IAAI,MAAM,mDAAmD;AAChF,iBAAW,OAAO;AAClB,WAAK,WAAW,MAAM,UAAU;AAAA,IACpC;AACA,UAAM,gBAAgB,IAAI,gBAAgB;AAC1C,SAAK,aAAa;AAClB,WAAO,cAAc;AAAA,EACzB;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK,YAAY;AACjB,YAAM,aAAa,IAAI,MAAM,gDAAgD;AAC7E,iBAAW,OAAO;AAClB,WAAK,WAAW,MAAM,UAAU;AAChC,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AACJ;AAQO,IAAM,uBAAuB,IAAI,yBAAyB;;;ACpCjE,IAAM,cAAc,CAAC,kBAAkB,UAAU;AAI1C,SAAS,0BAA0B,YAAY;AAClD,MAAI,CAAC,YAAY;AACb;AAAA,EACJ;AACA,MAAI,YAAY,QAAQ,UAAU,IAAI,GAAG;AACrC;AAAA,EACJ;AACA,SAAO;AACX;;;ACCA,eAAsB,kBAAkB,SAAS;AAE7C,MAAI,CAAC,QAAQ,eAAe,QAAQ,WAAW;AAC3C,YAAQ,KAAK,4TAA4T;AAEzU,cAAU,EAAE,aAAa,QAAQ;AAAA,EACrC;AACA,QAAM,EAAE,aAAa,kBAAkB,MAAM,IAAI;AACjD,MAAI,CAAC,wBAAwB,GAAG;AAC5B,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AAEA,QAAM,YAAY;AAAA,IACd,GAAG;AAAA,IACH,WAAW,wBAAwB,YAAY,SAAS;AAAA,IACxD,MAAM;AAAA,MACF,GAAG,YAAY;AAAA,MACf,IAAI,wBAAwB,YAAY,KAAK,EAAE;AAAA,IACnD;AAAA,IACA,oBAAoB,YAAY,oBAAoB,IAAI,+BAA+B;AAAA,EAC3F;AAEA,QAAM,gBAAgB,CAAC;AAMvB,MAAI,iBAAiB;AAEjB,kBAAc,YAAY;AAAA,EAC9B;AAEA,gBAAc,YAAY;AAE1B,gBAAc,SAAS,qBAAqB,qBAAqB;AAEjE,MAAI;AACJ,MAAI;AACA,iBAAc,MAAM,UAAU,YAAY,OAAO,aAAa;AAAA,EAClE,SACO,KAAK;AACR,UAAM,0BAA0B,EAAE,OAAO,KAAK,SAAS,cAAc,CAAC;AAAA,EAC1E;AACA,MAAI,CAAC,YAAY;AACb,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AACA,QAAM,EAAE,IAAI,OAAO,UAAU,KAAK,IAAI;AAEtC,MAAI,aAAa;AACjB,MAAI,OAAO,SAAS,kBAAkB,YAAY;AAC9C,iBAAa,SAAS,cAAc;AAAA,EACxC;AAEA,MAAI,6BAA6B;AACjC,MAAI,OAAO,SAAS,0BAA0B,YAAY;AACtD,QAAI;AACA,mCAA6B,SAAS,sBAAsB;AAAA,IAChE,SACO,OAAO;AACV,iCAA2B,2BAA2B,KAAK;AAAA,IAC/D;AAAA,EACJ;AACA,MAAI,oBAAoB;AACxB,MAAI,OAAO,SAAS,iBAAiB,YAAY;AAC7C,QAAI;AACA,YAAM,aAAa,SAAS,aAAa;AACzC,UAAI,eAAe,MAAM;AACrB,4BAAoB,wBAAwB,UAAU;AAAA,MAC1D;AAAA,IACJ,SACO,OAAO;AACV,iCAA2B,kBAAkB,KAAK;AAAA,IACtD;AAAA,EACJ;AAEA,MAAI;AACJ,MAAI,OAAO,SAAS,yBAAyB,YAAY;AACrD,QAAI;AACA,kCAA4B,wBAAwB,SAAS,qBAAqB,CAAC;AAAA,IACvF,SACO,OAAO;AACV,iCAA2B,0BAA0B,KAAK;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,wBAAwB,KAAK;AAAA,IACpC,UAAU;AAAA,MACN,mBAAmB,wBAAwB,SAAS,iBAAiB;AAAA,MACrE,gBAAgB,wBAAwB,SAAS,cAAc;AAAA,MAC/D;AAAA,MACA,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA,wBAAwB,WAAW,0BAA0B;AAAA,IAC7D,yBAAyB,0BAA0B,WAAW,uBAAuB;AAAA,EACzF;AACJ;AAKA,SAAS,2BAA2B,YAAY,OAAO;AACnD,UAAQ,KAAK,yFAAyF,UAAU;AAAA,GAA6C,KAAK;AACtK;;;ACnHO,SAAS,kCAAkC;AAC9C,MAAI,CAAC,wBAAwB,GAAG;AAC5B,WAAO,0CAA0C,SAAS,IAAI,QAAQ,CAAC,YAAY,QAAQ,KAAK,CAAC,CAAC;AAAA,EACtG;AAOA,QAAM,4BAA4B,WAC7B;AACL,MAAI,2BAA2B,oCAAoC,QAAW;AAC1E,WAAO,0CAA0C,SAAS,IAAI,QAAQ,CAAC,YAAY,QAAQ,KAAK,CAAC,CAAC;AAAA,EACtG;AACA,SAAO,0CAA0C,SAAS,0BAA0B,gCAAgC,CAAC;AACzH;AAEO,IAAM,4CAA4C;AAAA,EACrD,UAAU,CAAC,UAAU;AACzB;;;ACpBO,SAAS,4BAA4B,EAAE,OAAO,QAAS,GAAG;AAC7D,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,CAAC,WAAW;AACZ,UAAM,MAAM,iDAAiD;AAAA,EACjE;AACA,MAAI,MAAM,SAAS,cAAc;AAC7B,QAAI,QAAQ,kBAAkB,aAAa;AAEvC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,mBAAmB;AAKvC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS,MAAM;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL,WACS,MAAM,SAAS,iBAAiB;AACrC,UAAM,kBAAkB,WAAW,SAAS;AAC5C,QAAI,CAAC,cAAc,eAAe,GAAG;AAEjC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,GAAG,WAAW,SAAS,QAAQ;AAAA,QACxC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,SAAS,iBAAiB;AAEzC,aAAO,IAAI,cAAc;AAAA,QACrB,SAAS,cAAc,UAAU,IAAI;AAAA,QACrC,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,WACS,MAAM,SAAS,gBAAgB;AAGpC,WAAO,IAAI,cAAc;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAO;AACX;;;AC7CA,eAAsB,oBAAoB,SAAS;AAE/C,MAAI,CAAC,QAAQ,eAAe,QAAQ,WAAW;AAC3C,YAAQ,KAAK,8TAA8T;AAE3U,cAAU,EAAE,aAAa,QAAQ;AAAA,EACrC;AACA,QAAM,EAAE,aAAa,qBAAqB,OAAO,6BAA6B,KAAM,IAAI;AACxF,MAAI,CAAC,wBAAwB,GAAG;AAC5B,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AAGA,MAAI;AACJ,MAAI,YAAY,kBAAkB,WAAW,GAAG;AAC5C,uBAAmB,YAAY,kBAAkB,IAAI,+BAA+B;AAAA,EACxF;AAEA,QAAM,YAAY;AAAA,IACd,GAAG;AAAA,IACH,WAAW,wBAAwB,YAAY,SAAS;AAAA,IACxD;AAAA,EACJ;AAEA,QAAM,aAAa,CAAC;AAKpB,MAAI,oBAAoB;AACpB,QAAI,CAAE,MAAM,gCAAgC,GAAI;AAC5C,YAAM,MAAM,4CAA4C;AAAA,IAC5D;AAEA,UAAM,iBAAiB,SAAS,iBAAiB,iCAAiC;AAElF,QAAI,eAAe,SAAS,KAAK,4BAA4B;AACzD,YAAM,MAAM,mGAAmG;AAAA,IACnH;AAGA,eAAW,YAAY;AAEvB,cAAU,mBAAmB,CAAC;AAAA,EAClC;AAEA,aAAW,YAAY;AAEvB,aAAW,SAAS,qBAAqB,qBAAqB;AAE9D,MAAI;AACJ,MAAI;AACA,iBAAc,MAAM,UAAU,YAAY,IAAI,UAAU;AAAA,EAC5D,SACO,KAAK;AACR,UAAM,4BAA4B,EAAE,OAAO,KAAK,SAAS,WAAW,CAAC;AAAA,EACzE;AACA,MAAI,CAAC,YAAY;AACb,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACtD;AACA,QAAM,EAAE,IAAI,OAAO,UAAU,KAAK,IAAI;AACtC,MAAI,aAAa;AACjB,MAAI,SAAS,YAAY;AACrB,iBAAa,wBAAwB,SAAS,UAAU;AAAA,EAC5D;AAEA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,wBAAwB,KAAK;AAAA,IACpC,UAAU;AAAA,MACN,mBAAmB,wBAAwB,SAAS,iBAAiB;AAAA,MACrE,gBAAgB,wBAAwB,SAAS,cAAc;AAAA,MAC/D,WAAW,wBAAwB,SAAS,SAAS;AAAA,MACrD;AAAA,IACJ;AAAA,IACA;AAAA,IACA,wBAAwB,WAAW,0BAA0B;AAAA,IAC7D,yBAAyB,0BAA0B,WAAW,uBAAuB;AAAA,EACzF;AACJ;;;AC9FA,IAAM,kBAAkB,CAAC,YAAY;AACnC,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,eAAe;AAAA,MACb;AAAA,QACE,SAAS,CAAC,SAAS,KAAK,WAAW,cAAc;AAAA,QACjD,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,MACvB,sBAAsB;AAAA,MACtB,wBAAwB;AAAA,MACxB,qCAAqC;AAAA,IACvC;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,UACL,MAAM,UAAU,SAAS;AACvB,gBAAI,QAAQ,MAAM,mBAAmB;AACnC,kBAAI,SAAS,qBAAqB;AAChC,sBAAM,QAAQ,oBAAoB;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC7BA,IAAMC,qBAAoB;AAAA,EACxB,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS,CAAC,QAAQ,UAAU,QAAQ;AACtC;AACA,IAAMC,aAAY,oBAAoBD,kBAAiB;AACvD,IAAME,WAAUD,WAAU,QAAQ;AAAA,EAChC,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS,CAAC,QAAQ,UAAU,QAAQ;AACtC,CAAC;AACD,IAAM,SAASA,WAAU,QAAQ;AAAA,EAC/B,MAAM,CAAC;AAAA,EACP,SAAS,CAAC;AACZ,CAAC;AACD,IAAME,gBAAe;AAAA,EACnB,OAAOD;AAAA,EACP,MAAM;AACR;;;ACnCA,IAAME,iBAAgB,CAAC,UAAU;AAC/B,MAAI,MAAM,UAAU,MAAM,SAAS,cAAc,SAAS,MAAM,MAAM,GAAG;AACvE,WAAO;AAAA,EACT;AACA,MAAI,CAAC,MAAM,eAAe,CAAC,MAAM,YAAY;AAC3C,WAAO;AAAA,EACT;AACA,QAAM,SAAS,MAAM,QAAQ,MAAM,SAAS,eAAe,QAAQ,MAAM,GAAG;AAC5E,QAAM,UAAU,MAAM,SAAS,SAASC;AACxC,aAAWC,SAAQ,OAAO;AACxB,UAAM,QAAQ,QAAQA,KAAI;AAC1B,UAAM,SAAS,OAAO,UAAU,MAAM,cAAc,MAAM,WAAW;AACrE,QAAI,QAAQ,SAAS;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACNA,IAAM,qBAAqB,CAAC,YAAY;AACtC,QAAM,WAAW,KAAK,KAAK;AAC3B,QAAM,mBAAmB,KAAK,KAAK;AACnC,QAAM,sBAAsB,KAAK,KAAK;AACtC,QAAM,QAAQ;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,SAAS;AAAA,EACd;AACA,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,YAAY,CAAC,YAAY;AAAA,MACvB,QAAQ;AAAA,QACN,oBAAoB,CAAC;AAAA,QACrB,cAAc,CAAC;AAAA,QACf,YAAY,CAAC;AAAA,QACb,QAAQ,CAAC;AAAA,QACT,MAAM,CAAC;AAAA,MACT;AAAA,MACA,cAAc;AAAA,QACZ,qBAAqB,CAAC,SAAS;AAC7B,gBAAM,eAAe,cAAc;AAAA,YACjC,MAAM,KAAK;AAAA,YACX,SAAS;AAAA,cACP,IAAI,SAAS;AAAA,cACb;AAAA,YACF;AAAA,YACA,aAAa,KAAK,eAAe,KAAK;AAAA,UACxC,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU,CAAC,WAAW;AACpB,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AACA,YAAM,qBAAqB;AAAA,QACzB,CAAC,gBAAgB;AAAA,QACjB;AAAA,QACA;AAAA,QACA,OAAO;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,CAAC,mBAAmB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,uCAAuC;AAAA,IACzC;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,SAAS,0BAA0B,SAAS,0BAA0B,SAAS;AAAA,QACxF;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,KAAK,WAAW,eAAe;AAAA,QACxC;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,KAAK,WAAW,0BAA0B;AAAA,QACnD;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,KAAK,SAAS,kCAAkC;AAAA,QACzD;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,2BAA2B,CAAC,WAAW;AAC3C,SAAO,CAAC;AACV;AAEA,IAAM,iBAAiB,MAAM;AAC3B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,oBAAoB,CAAC,QAAQ;AAAA,EACjC;AACF,MAAM;AACJ,QAAM,gBAAgB,OAAO,MAAM,YAAY;AAC7C,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,OAAO,MAAM;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,SAAS,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI;AACF,YAAM,MAAM,MAAM,oBAAoB;AAAA,QACpC,aAAa,SAAS;AAAA,QACtB,oBAAoB,MAAM;AAAA,MAC5B,CAAC;AACD,YAAM,WAAW,MAAM,OAAO,kCAAkC;AAAA,QAC9D,MAAM;AAAA,UACJ,UAAU;AAAA,QACZ;AAAA,QACA,GAAG,MAAM;AAAA,QACT,GAAG;AAAA,QACH,QAAQ;AAAA,MACV,CAAC;AACD,UAAI,CAAC,SAAS,MAAM;AAClB,eAAO;AAAA,MACT;AAAA,IACF,SAAS,GAAG;AACV,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,OAAO,MAAM,cAAc;AACjD,UAAM,UAAU,MAAM;AAAA,MACpB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,OAAO;AAAA,UACL,GAAG,MAAM,2BAA2B;AAAA,YAClC,yBAAyB,KAAK;AAAA,UAChC;AAAA,UACA,GAAG,MAAM,QAAQ;AAAA,YACf,MAAM,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,MAAM;AACjB,aAAO;AAAA,IACT;AACA,QAAI;AACF,YAAM,MAAM,MAAM,kBAAkB;AAAA,QAClC,aAAa,QAAQ;AAAA,QACrB,iBAAiB,MAAM;AAAA,MACzB,CAAC;AACD,YAAM,WAAW,MAAM,OAAO,gCAAgC;AAAA,QAC5D,GAAG,MAAM;AAAA,QACT,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,UAAU;AAAA,UACV,MAAM,MAAM;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AACD,UAAI,CAAC,SAAS,MAAM;AAClB,eAAO;AAAA,MACT;AACA,oBAAc,IAAI,KAAK,OAAO,CAAC;AAAA,IACjC,SAAS,GAAG;AACV,UAAI,aAAa,eAAe;AAC9B,YAAI,EAAE,SAAS,6CAA6C;AAC1D,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,cACL,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AACA,YAAI,EAAE,SAAS,0BAA0B;AACvC,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,cACL,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,YACL,SAAS,EAAE;AAAA,YACX,QAAQ;AAAA,YACR,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS,aAAa,QAAQ,EAAE,UAAU;AAAA,UAC1C,QAAQ;AAAA,UACR,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA;AAAA;AAAA;AAAA,MAIP,YAAY;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAIA,QAAQ,CAAC;AAAA,EACX;AACF;AACA,IAAM,gBAAgB,MAAM;AAC1B,QAAM,gBAAgB,KAAK;AAC3B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,YAAY,CAAC,WAAW,kBAAkB,QAAQ;AAAA,MAChD;AAAA,IACF,CAAC;AAAA,IACD,SAAS,QAAQ;AACf,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,qBAAqB;AAAA,MACrB,yBAAyB;AAAA,IAC3B;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,SAAS,kCAAkC,SAAS,6BAA6B,SAAS;AAAA,QACnG;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,MAAM;AAC5B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,oBAAoB,MAAM;AAC9B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,eAAe;AAAA,MACb;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,SAAS,0BAA0B,SAAS;AAAA,QACrD;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,MAAM;AAC5B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,aAAa;AAAA,MACX,sBAAsB;AAAA,IACxB;AAAA,EACF;AACF;AAEA,IAAM,wBAAwB,CAAC,WAAW;AACxC,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,cAAc,CAAC,YAAY;AAC/B,QAAM,QAAQ;AAAA,IACZ,OAAOC;AAAA,IACP,MAAM;AAAA,IACN,GAAG,SAAS;AAAA,EACd;AACA,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,YAAY,OAAO;AAAA,MACjB,OAAO;AAAA,QACL,qBAAqB,CAAC,SAAS;AAC7B,gBAAM,eAAeC,eAAgB;AAAA,YACnC,MAAM,KAAK;AAAA,YACX,SAAS;AAAA,cACP,IAAI,SAAS;AAAA,cACb;AAAA,YACF;AAAA,YACA,aAAa,KAAK,eAAe,KAAK;AAAA,UACxC,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,qBAAqB;AAAA,MACrB,6BAA6B;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,MAAM;AAC/B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,YAAY,MAAM;AACtB,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,qBAAqB,MAAM;AAC/B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,eAAe;AAAA,MACb;AAAA,QACE,QAAQ,MAAM;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB,MAAM;AAC3B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAI,sBAAsB;AAC1B,IAAM,eAAe,CAAC,YAAY;AAChC,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,YAAY,CAAC,QAAQ,OAAO;AAAA,MAC1B,QAAQ,OAAO,MAAM,iBAAiB;AACpC,YAAI,qBAAqB;AACvB,kBAAQ;AAAA,YACN;AAAA,UACF;AACA;AAAA,QACF;AACA,8BAAsB;AACtB,YAAI;AACF,cAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU;AACrD,oBAAQ;AAAA,cACN;AAAA,YACF;AACA;AAAA,UACF;AACA,gBAAM,EAAE,YAAY,oBAAoB,QAAQ,IAAI,QAAQ,CAAC;AAC7D,gBAAM,eAAe,WAAW,QAAQ,WAAW;AACnD,gBAAM,iBAAiB;AACvB,gBAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrC,gBAAI,aAAa;AACjB,kBAAM,YAAY,QAAQ,eAAe,aAAa;AACtD,kBAAM,cAAc,QAAQ,eAAe,eAAe;AAC1D,mBAAO,QAAQ,SAAS,GAAG,WAAW;AAAA,cACpC,WAAW,QAAQ;AAAA,cACnB,UAAU,OAAO,aAAa;AAC5B,6BAAa;AACb,oBAAI;AACF,wBAAM,OAAO,qBAAqB;AAAA,oBAChC,QAAQ;AAAA,oBACR,MAAM,EAAE,SAAS,SAAS,WAAW;AAAA,oBACrC,GAAG,MAAM;AAAA,oBACT,GAAG;AAAA,kBACL,CAAC;AACD,sBAAI,CAAC,MAAM,gBAAgB,CAAC,gBAAgB,MAAM,aAAa;AAC7D,2BAAO,SAAS,OAAO,MAAM,eAAe;AAAA,kBAC9C;AACA,0BAAQ;AAAA,gBACV,SAAS,OAAO;AACd,0BAAQ,MAAM,kCAAkC,KAAK;AACrD,yBAAO,KAAK;AAAA,gBACd;AAAA,cACF;AAAA,cACA,aAAa;AAAA,cACb,uBAAuB;AAAA,cACvB,SAAS;AAAA,cACT,GAAG,QAAQ;AAAA,YACb,CAAC;AACD,kBAAM,eAAe,CAAC,YAAY;AAChC,kBAAI,WAAY;AAChB,qBAAO,QAAQ,SAAS,GAAG,OAAO,CAAC,iBAAiB;AAClD,oBAAI,WAAY;AAChB,oBAAI,aAAa,qBAAqB,aAAa,kBAAkB,GAAG;AACtE,sBAAI,UAAU,aAAa;AACzB,0BAAM,QAAQ,KAAK,IAAI,GAAG,OAAO,IAAI;AACrC,+BAAW,MAAM,aAAa,UAAU,CAAC,GAAG,KAAK;AAAA,kBACnD,OAAO;AACL,0BAAM,uBAAuB,YAAY;AAAA,kBAC3C;AAAA,gBACF,WAAW,aAAa,mBAAmB,aAAa,gBAAgB,GAAG;AACzE,sBAAI,UAAU,aAAa;AACzB,0BAAM,QAAQ,KAAK,IAAI,GAAG,OAAO,IAAI;AACrC,+BAAW,MAAM,aAAa,UAAU,CAAC,GAAG,KAAK;AAAA,kBACnD,OAAO;AACL,0BAAM,uBAAuB,YAAY;AAAA,kBAC3C;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AACA,yBAAa,CAAC;AAAA,UAChB,CAAC;AAAA,QACH,SAAS,OAAO;AACd,kBAAQ,MAAM,qCAAqC,KAAK;AACxD,gBAAM;AAAA,QACR,UAAE;AACA,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS,QAAQ;AACf,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAM,mBAAmB,MAAM;AAC7B,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,QAAI,OAAO,yBAAyB;AAClC,cAAQ;AACR;AAAA,IACF;AACA,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,MAAM;AACb,WAAO,QAAQ;AACf,WAAO,QAAQ;AACf,WAAO,SAAS,MAAM;AACpB,aAAO,0BAA0B;AACjC,cAAQ;AAAA,IACV;AACA,aAAS,KAAK,YAAY,MAAM;AAAA,EAClC,CAAC;AACH;AAEA,IAAM,sBAAsB,MAAM;AAChC,SAAO,kBAAkB;AAC3B;AAEA,IAAM,oBAAoB,MAAM;AAC9B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,YAAY,MAAM;AACtB,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,aAAa,MAAM;AACvB,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,eAAe,MAAM;AACzB,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,IACrB,aAAa;AAAA,MACX,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,wCAAwC;AAAA,IAC1C;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,MAAM;AAC/B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;AAEA,IAAM,aAAa,MAAM;AACvB,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,oBAAoB,CAAC;AAAA,EACvB;AACF;", "names": ["role", "defaultStatements", "defaultAc", "adminAc", "defaultRoles", "hasPermission", "defaultRoles", "role", "adminAc", "hasPermission"]}