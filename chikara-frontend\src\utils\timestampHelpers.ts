/**
 * Utility functions for safely handling BigInt timestamps from the backend
 */

/**
 * Safely converts BigInt timestamps to numbers for use in Date objects and calculations.
 * Handles undefined/null values and various input types.
 *
 * @param timestamp - The timestamp value (can be BigInt, string, number, undefined, or null)
 * @returns A number timestamp in milliseconds, or 0 if invalid
 */
export const safeTimestampToNumber = (timestamp: string | bigint | number | undefined | null): number => {
    if (timestamp === undefined || timestamp === null) {
        return 0;
    }

    if (typeof timestamp === "number") {
        return timestamp;
    }

    // Handle string values – could be either a pure numeric string (e.g. "1718135043000")
    // or an ISO-8601 date string (e.g. "2025-07-11T15:09:44.000Z").
    if (typeof timestamp === "string") {
        const trimmed = timestamp.trim();

        // Numeric only? Treat as milliseconds since epoch
        if (/^\d+$/.test(trimmed)) {
            const parsedNumeric = Number(trimmed);
            return isNaN(parsedNumeric) ? 0 : parsedNumeric;
        }

        // Otherwise, attempt to parse as a date string
        const parsedDate = Date.parse(trimmed);
        return isNaN(parsedDate) ? 0 : parsedDate;
    }

    if (typeof timestamp === "bigint") {
        return Number(timestamp);
    }

    return 0;
};

/**
 * Safely converts a timestamp to a Date object.
 *
 * @param timestamp - The timestamp value
 * @returns A Date object, or null if the timestamp is invalid
 */
export const safeTimestampToDate = (timestamp: string | bigint | number | undefined | null): Date | null => {
    const numericTimestamp = safeTimestampToNumber(timestamp);

    if (numericTimestamp === 0) {
        return null;
    }

    const date = new Date(numericTimestamp);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
        return null;
    }

    return date;
};

/**
 * Checks if a timestamp has expired (is in the past).
 *
 * @param timestamp - The timestamp to check
 * @returns true if the timestamp has passed, false otherwise
 */
export const hasTimestampExpired = (timestamp: string | bigint | number | undefined | null): boolean => {
    const numericTimestamp = safeTimestampToNumber(timestamp);

    if (numericTimestamp === 0) {
        return false;
    }

    return numericTimestamp <= Date.now();
};

/**
 * Calculates milliseconds remaining until a timestamp.
 *
 * @param timestamp - The target timestamp
 * @returns Milliseconds remaining (negative if in the past), or -1 if invalid
 */
export const getMillisecondsUntilTimestamp = (timestamp: string | bigint | number | undefined | null): number => {
    const numericTimestamp = safeTimestampToNumber(timestamp);

    if (numericTimestamp === 0) {
        return -1;
    }

    return numericTimestamp - Date.now();
};

/**
 * Checks if a timestamp exists and is valid.
 *
 * @param timestamp - The timestamp to validate
 * @returns true if the timestamp exists and represents a valid date, false otherwise
 */
export const isTimestampValid = (timestamp: string | bigint | number | undefined | null): boolean => {
    if (timestamp === undefined || timestamp === null) {
        return false;
    }

    const dateTimestamp = safeTimestampToNumber(timestamp);
    return dateTimestamp !== null && dateTimestamp > 0;
};
