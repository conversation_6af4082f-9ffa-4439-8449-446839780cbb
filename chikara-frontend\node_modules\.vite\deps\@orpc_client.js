import {
  COMMON_ORPC_ERROR_DEFS,
  ORPCError,
  createORPCError<PERSON><PERSON><PERSON><PERSON>,
  fallbackORPCErrorMessage,
  fallbackORPCErrorStatus,
  isDefinedError,
  isORPCError<PERSON>son,
  isORPCErrorStatus,
  mapEventIterator,
  toORPCError
} from "./chunk-EDR6GG6B.js";
import {
  ErrorEvent
} from "./chunk-RGQIEPFB.js";
import {
  AsyncIteratorClass,
  EventPublisher,
  asyncIteratorToStream,
  isTypescriptObject,
  onError,
  onFinish,
  onStart,
  onSuccess,
  streamToAsyncIteratorClass
} from "./chunk-2S4BHSNR.js";
import "./chunk-PR4QN5HX.js";

// ../node_modules/.bun/@orpc+client@1.8.0/node_modules/@orpc/client/dist/index.mjs
async function safe(promise) {
  try {
    const output = await promise;
    return Object.assign(
      [null, output, false, true],
      { error: null, data: output, isDefined: false, isSuccess: true }
    );
  } catch (e) {
    const error = e;
    if (isDefinedError(error)) {
      return Object.assign(
        [error, void 0, true, false],
        { error, data: void 0, isDefined: true, isSuccess: false }
      );
    }
    return Object.assign(
      [error, void 0, false, false],
      { error, data: void 0, isDefined: false, isSuccess: false }
    );
  }
}
function resolveFriendlyClientOptions(options) {
  return {
    ...options,
    context: options.context ?? {}
    // Context only optional if all fields are optional
  };
}
function createORPCClient(link, options = {}) {
  const path = options.path ?? [];
  const procedureClient = async (...[input, options2 = {}]) => {
    return await link.call(path, input, resolveFriendlyClientOptions(options2));
  };
  const recursive = new Proxy(procedureClient, {
    get(target, key) {
      if (typeof key !== "string") {
        return Reflect.get(target, key);
      }
      return createORPCClient(link, {
        ...options,
        path: [...path, key]
      });
    }
  });
  return recursive;
}
function createSafeClient(client) {
  const proxy = new Proxy((...args) => safe(client(...args)), {
    get(_, prop, receiver) {
      const value = Reflect.get(client, prop, receiver);
      if (typeof prop !== "string") {
        return value;
      }
      if (!isTypescriptObject(value)) {
        return value;
      }
      return createSafeClient(value);
    }
  });
  return proxy;
}
var DynamicLink = class {
  constructor(linkResolver) {
    this.linkResolver = linkResolver;
  }
  async call(path, input, options) {
    const resolvedLink = await this.linkResolver(options, path, input);
    const output = await resolvedLink.call(path, input, options);
    return output;
  }
};
export {
  AsyncIteratorClass,
  COMMON_ORPC_ERROR_DEFS,
  DynamicLink,
  ErrorEvent,
  EventPublisher,
  ORPCError,
  createORPCClient,
  createORPCErrorFromJson,
  createSafeClient,
  asyncIteratorToStream as eventIteratorToStream,
  fallbackORPCErrorMessage,
  fallbackORPCErrorStatus,
  isDefinedError,
  isORPCErrorJson,
  isORPCErrorStatus,
  mapEventIterator,
  onError,
  onFinish,
  onStart,
  onSuccess,
  resolveFriendlyClientOptions,
  safe,
  streamToAsyncIteratorClass as streamToEventIterator,
  toORPCError
};
//# sourceMappingURL=@orpc_client.js.map
