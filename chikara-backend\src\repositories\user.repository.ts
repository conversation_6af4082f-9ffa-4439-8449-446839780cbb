import { TransactionClient, db } from "../lib/db.js";
import { EquipSlots, ItemQuality, Prisma } from "@prisma/client";

export const getUserById = async (userId: number, options?: Omit<Prisma.userFindUniqueArgs, "where">) => {
    return await db.user.findUnique({
        where: { id: userId },
        ...options,
    });
};

export const checkUserExists = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: { id: true },
    });
};

export const getUserWithAchievements = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        include: {
            user_achievements: { select: { totalMissionHours: true } },
        },
    });
};

export const getUserProfile = async (userId: number) => {
    const attributes = {
        id: true,
        username: true,
        about: true,
        userType: true,
        avatar: true,
        level: true,
        hospitalisedUntil: true,
        hospitalisedReason: true,
        jailedUntil: true,
        jailReason: true,
        createdAt: true,
        gangId: true,
        profileBanner: true,
        class: true,
        gang: {
            select: {
                id: true,
                name: true,
            },
        },
    };

    return await db.user.findUnique({
        where: { id: userId },
        select: attributes,
    });
};

export const findUserInventory = async (userId: number) => {
    const inventory = await db.user_item.findMany({
        where: { userId },
        select: {
            id: true,
            count: true,
            upgradeLevel: true,
            isTradeable: true,
            quality: true,
            item: {
                omit: {
                    createdAt: true,
                    updatedAt: true,
                },
            },
        },
    });

    return inventory;
};

export const findTradeableInventory = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        include: {
            user_item: {
                where: { isTradeable: true },
                select: {
                    id: true,
                    count: true,
                    upgradeLevel: true,
                    isTradeable: true,
                    quality: true,
                    item: {
                        omit: {
                            createdAt: true,
                            updatedAt: true,
                        },
                    },
                },
            },
        },
    });
};

export const getAllUsers = async (page = 1, limit = 50) => {
    const skip = (page - 1) * limit;
    
    const [users, totalCount] = await Promise.all([
        db.user.findMany({
            take: limit,
            skip: skip,
            orderBy: {
                id: 'asc',
            },
            select: {
                id: true,
                username: true,
                userType: true,
                avatar: true,
                level: true,
                class: true,
                classPoints: true,
                gang: {
                    select: {
                        id: true,
                        name: true,
                        avatar: true,
                    },
                },
            },
        }),
        db.user.count(),
    ]);

    return { users, totalCount };
};

export interface UserProfileUpdateValues {
    about?: string;
    username?: string;
    avatar?: string;
    profileBanner?: string;
    [key: string]: unknown;
}

export const updateUserProfile = async (userId: number, updateValues: UserProfileUpdateValues) => {
    return await db.user.update({
        where: { id: userId },
        data: updateValues,
    });
};

export const findUserByUsername = async (username: string) => {
    return await db.user.findFirst({
        where: { username },
    });
};

export interface UserStatsUpdateValues {
    [key: string]: unknown;
    lastEnergyTick?: bigint | number | null;
    energy?: number;
    focus?: number;
    dailyFatigueUsed?: number;
    lastFatigueReset?: Date | null;
}

export const updateUserStats = async (user: { id: number }, statsToUpdate: UserStatsUpdateValues) => {
    // Convert Date objects to BigInt if present
    const formattedStats: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(statsToUpdate)) {
        if (key === "lastEnergyTick" && value instanceof Date) {
            formattedStats[key] = BigInt(value.getTime());
        } else if (key === "lastFatigueReset" && value instanceof Date) {
            // Keep lastFatigueReset as a Date since it's a Date field in the schema
            formattedStats[key] = value;
        } else {
            formattedStats[key] = value;
        }
    }

    return await db.user.update({
        where: { id: user.id },
        data: formattedStats,
    });
};

export const findUserStatusEffects = async (userId: number) => {
    const now = Date.now();
    return await db.user_status_effect.findMany({
        where: {
            userId,
            endsAt: { gt: BigInt(now) },
        },
        select: {
            id: true,
            endsAt: true,
            userId: true,
            stacks: true,
            customName: true,
            effect: true,
        },
    });
};

export const createUserRecipe = async (userId: number, craftingRecipeId: number) => {
    return await db.user_recipe.create({
        data: {
            userId,
            craftingRecipeId,
        },
    });
};

export const findUserRecipe = async (userId: number, craftingRecipeId: number) => {
    return await db.user_recipe.findFirst({
        where: {
            userId,
            craftingRecipeId,
        },
    });
};

export const updateLastNewsIDRead = async (userId: number, newsId: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { lastNewsIDRead: newsId },
    });
};

export const findUserByIdWithGang = async (id: number) => {
    return await db.user.findUnique({
        where: { id },
        include: { gang: true },
    });
};

export const findUserItemByUserIdAndItemId = async (
    userId: number,
    itemId: number,
    isTradeable?: boolean,
    upgradeLevel?: number,
    quality?: ItemQuality,
    tx?: TransactionClient
) => {
    if (tx) {
        return await tx.user_item.findFirst({
            where: { userId, itemId, isTradeable, upgradeLevel, quality },
        });
    }
    return await db.user_item.findFirst({
        where: { userId, itemId, isTradeable, upgradeLevel, quality },
    });
};

export const createUserItem = async (
    userId: number,
    itemId: number,
    count: number,
    isTradeable = false,
    upgradeLevel?: number,
    quality?: ItemQuality,
    tx?: TransactionClient
) => {
    const data = {
        userId,
        itemId,
        count,
        isTradeable,
        upgradeLevel,
        quality,
    };

    if (tx) {
        return await tx.user_item.create({
            data,
            include: {
                item: true,
            },
        });
    }

    return await db.user_item.create({
        data,
        include: {
            item: true,
        },
    });
};

export const updateUserItemCount = async (userItem: { id: number }, newCount: number, tx?: TransactionClient) => {
    if (tx) {
        return await tx.user_item.update({
            where: { id: userItem.id },
            data: { count: newCount },
            include: {
                item: true,
            },
        });
    }

    return await db.user_item.update({
        where: { id: userItem.id },
        data: { count: newCount },
        include: {
            item: true,
        },
    });
};

export const findUserItemById = async (userItemId: number, tx?: TransactionClient) => {
    if (tx) {
        return await tx.user_item.findUnique({
            where: { id: userItemId },
            include: { item: true },
        });
    }
    return await db.user_item.findUnique({
        where: { id: userItemId },
        include: { item: true },
    });
};

export const deleteUserItem = async (userItem: { id: number }, tx?: TransactionClient) => {
    if (tx) {
        return await tx.user_item.delete({
            where: { id: userItem.id },
        });
    }
    return await db.user_item.delete({
        where: { id: userItem.id },
    });
};

export const findAllUserItemsByUserIdAndItemId = async (
    userId: number,
    itemId: number,
    isTradeable?: boolean,
    upgradeLevel?: number,
    tx?: TransactionClient
) => {
    if (tx) {
        return await tx.user_item.findMany({
            where: { userId, itemId, isTradeable, upgradeLevel },
            orderBy: { count: "asc" },
        });
    }
    return await db.user_item.findMany({
        where: { userId, itemId, isTradeable, upgradeLevel },
        orderBy: { count: "asc" },
    });
};

export const findTradeableUserItems = async (userId: number, itemId: number) => {
    return await db.user_item.findMany({
        where: { userId, itemId, isTradeable: true },
    });
};

export const upsertEquippedItem = async (userId: number, data: { slot: EquipSlots; userItemId: number }) => {
    return await db.equipped_item.upsert({
        where: {
            userId_slot: {
                userId,
                slot: data.slot,
            },
        },
        update: {
            userItemId: data.userItemId,
        },
        create: {
            userId,
            slot: data.slot,
            userItemId: data.userItemId,
        },
    });
};

export const findUserForChat = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            username: true,
            avatar: true,
            userType: true,
            level: true,
            chatBannedUntil: true,
        },
    });
};

export const findUserWithMiningSkills = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        include: {
            user_skills: {
                where: { skillType: "mining" },
            },
        },
    });
};

export const findUserByEmail = async (email: string) => {
    return await db.user.findUnique({
        where: { email },
    });
};

export const findUserHealthInfo = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            username: true,
            currentHealth: true,
            hospitalisedUntil: true,
            userType: true,
            health: true,
            jailedUntil: true,
            jailReason: true,
        },
    });
};

export const findUserWithGangInfo = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            gangId: true,
        },
    });
};

export const updateUserCash = async (userId: number, newCashAmount: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { cash: newCashAmount },
    });
};

export const incrementUserCash = async (userId: number, amount: number) => {
    if (amount <= 0) {
        throw new Error("Amount must be positive");
    }
    return await db.user.update({
        where: { id: userId },
        data: { cash: { increment: amount } },
    });
};

export const decrementUserCash = async (userId: number, amount: number) => {
    if (amount <= 0) {
        throw new Error("Amount must be positive");
    }
    return await db.user.update({
        where: { id: userId },
        data: { cash: { decrement: amount } },
    });
};
