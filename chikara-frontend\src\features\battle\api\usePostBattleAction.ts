import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const usePostBattleAction = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.postBattleAction.mutationOptions({
            onSuccess: () => {
                // Only invalidate user info to refresh UI - battle is over after post-battle action
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
            },
        })
    );
};

export default usePostBattleAction;
