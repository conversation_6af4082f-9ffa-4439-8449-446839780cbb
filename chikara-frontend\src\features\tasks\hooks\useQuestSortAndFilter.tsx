import type { QuestWithProgress } from "@/types/quest";
import useSortAndFilter from "@/hooks/useSortAndFilter";
import { getQuestStatus } from "@/features/tasks/helpers/questHelpers";

// Sort options
export type QuestSortOption = "level" | "name" | "quest_giver" | "Yen_reward" | "XP_reward" | "REP_reward";
export type { SortDirection } from "@/hooks/useSortAndFilter";

// Filter options
export type QuestFilterOption = {
    status?: "available" | "in_progress" | "complete" | "ready_to_complete" | "all";
    minLevel?: number;
    maxLevel?: number;
    questGiverId?: number;
    hasItemRewards?: boolean;
    hasTalentPointRewards?: boolean;
    location?: "church" | "shrine" | "mall" | "alley" | "school" | "sewers" | "themepark" | "any";
    isStoryQuest?: boolean;
    chapterId?: number;
    searchTerm?: string;
};

export default function useQuestSortAndFilter(quests: QuestWithProgress[] = []) {
    const {
        filteredAndSortedData: filteredAndSortedQuests,
        sortOption,
        setSortOption,
        sortDirection,
        setSortDirection,
        filterOptions,
        setFilterOptions,
        resetFilters,
        hasActiveFilters,
        totalItems,
        filteredItems,
    } = useSortAndFilter<QuestWithProgress, QuestSortOption>({
        data: quests,
        defaultSortOption: "level",
        defaultSortDirection: "asc",
        defaultFilters: {
            status: "all",
        },
        config: {
            searchFields: ["name", "description"],
            customFilters: {
                status: (quest, value) => {
                    if (value === "all") return true;
                    const status = getQuestStatus(quest);
                    return status === value;
                },
                minLevel: (quest, value) => quest.levelReq >= value,
                maxLevel: (quest, value) => quest.levelReq <= value,
                questGiverId: (quest, value) => quest.shopId === value,
                hasItemRewards: (quest, value) => {
                    if (value === true) {
                        return quest.quest_reward.some((reward) => reward.rewardType === "ITEM");
                    }
                    return true;
                },
                hasTalentPointRewards: (quest, value) => {
                    if (value === true) {
                        return quest.talentPointReward > 0;
                    }
                    return true;
                },
                location: (quest, value) => {
                    if (value === "any") return true;
                    return quest.quest_objective.some((objective) => objective.location?.toLowerCase() === value);
                },
                isStoryQuest: (quest, value) => quest.isStoryQuest === value,
                chapterId: (quest, value) => quest.chapterId === value,
            },
            customSorters: {
                level: (a, b) => a.levelReq - b.levelReq,
                name: (a, b) => a.name.localeCompare(b.name),
                quest_giver: (a, b) => (a.shopId || 0) - (b.shopId || 0),
                Yen_reward: (a, b) => (b.cashReward || 0) - (a.cashReward || 0), // Descending by default for rewards
                XP_reward: (a, b) => (b.xpReward || 0) - (a.xpReward || 0),
                REP_reward: (a, b) => (b.repReward || 0) - (a.repReward || 0),
            },
        },
    });

    return {
        filteredAndSortedQuests,
        sortOption,
        setSortOption,
        sortDirection,
        setSortDirection,
        filterOptions,
        setFilterOptions,
        resetFilters,
        hasActiveFilters,
        totalQuests: totalItems,
        filteredQuests: filteredItems,
    };
}
