import type { QuestWithProgress } from "@/types/quest";

// Get quest status in a more reusable way
export const getQuestStatus = (
    quest: QuestWithProgress
): "available" | "in_progress" | "complete" | "ready_to_complete" => {
    if (!quest.quest_progress || quest.quest_progress.length === 0) {
        return "available";
    }
    return quest.quest_progress[0].questStatus as "available" | "in_progress" | "complete" | "ready_to_complete";
};