import "@/features/battle/battle.css";
import { useBattleStatus } from "@/features/battle/api/useBattleStatus";
import { useStartAttack, BattleAttackResponse } from "@/features/battle/api/useStartAttack";
import DeathPanel from "@/features/battle/components/BattleDeathPanel";
import BattleLoadingScreen from "@/features/battle/components/BattleLoadingScreen";
import BattleLog from "@/features/battle/components/BattleLog";
import BattlePanel from "@/features/battle/components/BattlePanel";
import VictoryPanel from "@/features/battle/components/BattleVictoryPanel";
import { PlayerStatus } from "@/features/battle/components/PlayerStatus";
import type { BattleAction, BattleState, CombatLogEntry, BattlePlayer } from "@/features/battle/types/battle";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useEffect, useState, useReducer } from "react";
import { ScrollText } from "lucide-react";
import { resolveBattleParticipants } from "@/features/battle/utils/battleUtils";

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

interface AnimationState {
    attackedPlayer: string | null;
    playerDamage?: number;
    enemyDamage?: number;
    isAnimating: boolean;
    fleeSuccess: boolean;
}

type AnimationAction =
    | { type: "BATTLE_RESET" }
    | { type: "ATTACK_SEQUENCE_STARTED" }
    | {
          type: "ATTACK_EXECUTED";
          payload: { attackedPlayer: string | null; playerDamage?: number; enemyDamage?: number };
      }
    | { type: "DAMAGE_CLEARED" }
    | { type: "ATTACK_SEQUENCE_COMPLETED" }
    | { type: "FLEE_SUCCEEDED" };

const animationReducer = (state: AnimationState, action: AnimationAction): AnimationState => {
    switch (action.type) {
        case "BATTLE_RESET":
            return {
                attackedPlayer: null,
                playerDamage: undefined,
                enemyDamage: undefined,
                isAnimating: false,
                fleeSuccess: false,
            };
        case "ATTACK_SEQUENCE_STARTED":
            return { ...state, isAnimating: true };
        case "ATTACK_EXECUTED":
            return { ...state, ...action.payload };
        case "DAMAGE_CLEARED":
            return {
                ...state,
                attackedPlayer: null,
                playerDamage: undefined,
                enemyDamage: undefined,
            };
        case "ATTACK_SEQUENCE_COMPLETED":
            return {
                ...state,
                attackedPlayer: null,
                playerDamage: undefined,
                enemyDamage: undefined,
                isAnimating: false,
            };
        case "FLEE_SUCCEEDED":
            return { ...state, fleeSuccess: true };
        default:
            return state;
    }
};

function Battle() {
    const [battleState, setBattleState] = useState<BattleState | null>(null);
    const [loading, setLoading] = useState(false);
    const [animationState, dispatchAnimation] = useReducer(animationReducer, {
        attackedPlayer: null,
        playerDamage: undefined,
        enemyDamage: undefined,
        isAnimating: false,
        fleeSuccess: false,
    });
    const [healAmount] = useState<number | undefined>();
    const [combatLogs, setCombatLogs] = useState<CombatLogEntry[]>([]);

    const { data: currentUser, isLoading, error: currentUserError } = useFetchCurrentUser();
    const {
        data: battleStatus,
        isLoading: isBattleStatusLoading,
        error: battleStatusError,
        isFetchedAfterMount,
    } = useBattleStatus();
    const startAttackMutation = useStartAttack();

    // Utility functions
    const clearDamageDisplay = () => {
        dispatchAnimation({ type: "DAMAGE_CLEARED" });
    };

    const mergeCombatLogs = (newState: BattleState): BattleState => {
        const existingLogs = battleState?.combatLog || [];
        const newLogs = Array.isArray(newState.combatLog) ? newState.combatLog : [newState.combatLog];
        const filteredNewLogs = newLogs.filter((log): log is CombatLogEntry => log !== undefined);

        return {
            ...newState,
            combatLog: [...existingLogs, ...filteredNewLogs],
        };
    };

    const updateCombatLogs = (newLogs: CombatLogEntry[]) => {
        setCombatLogs((prev) => [...prev, ...newLogs]);
    };

    const updateIntermediateBattleState = (
        attackerId: string,
        result: BattleAttackResponse,
        player: BattlePlayer,
        enemy: BattlePlayer
    ): BattleState => {
        const targetId = attackerId === player.id ? enemy.id : player.id;

        return {
            ...result.battleState,
            players: {
                ...battleState!.players,
                [targetId]: {
                    ...battleState!.players[targetId],
                    currentHealth: result.battleState.players[targetId].currentHealth,
                },
            },
        };
    };

    const processAttackData = (result: BattleAttackResponse, player: BattlePlayer, enemy: BattlePlayer) => {
        const firstAttacker = result.attackedFirst === player.id ? player : enemy;
        const secondAttacker = result.attackedFirst === player.id ? enemy : player;
        const firstDamage =
            (result.attackedFirst === player.id ? result.playerAction.damage : result.enemyAction.damage) || undefined;
        const secondDamage =
            (result.attackedFirst === player.id ? result.enemyAction.damage : result.playerAction.damage) || undefined;

        const newLogs = Array.isArray(result.battleState.combatLog)
            ? result.battleState.combatLog
            : [result.battleState.combatLog];
        const filteredNewLogs = newLogs.filter((log): log is CombatLogEntry => log !== undefined);

        return { firstAttacker, secondAttacker, firstDamage, secondDamage, filteredNewLogs };
    };

    const animateAttackStep = async (
        attackedPlayerId: string | null,
        playerDamage?: number,
        enemyDamage?: number,
        newBattleState?: BattleState
    ) => {
        dispatchAnimation({
            type: "ATTACK_EXECUTED",
            payload: {
                attackedPlayer: attackedPlayerId,
                playerDamage,
                enemyDamage,
            },
        });

        if (newBattleState) {
            setBattleState(newBattleState);
        }

        await delay(1000);
        clearDamageDisplay();
    };

    const executeTurnAnimation = async (
        actorId: string,
        damage: number | undefined,
        logs: CombatLogEntry[],
        result: BattleAttackResponse,
        isFirstAttack: boolean,
        mergedBattleState: BattleState,
        player: BattlePlayer,
        enemy: BattlePlayer
    ) => {
        const filteredActorLogs = logs.filter((log) => log.actorId === actorId);
        updateCombatLogs(filteredActorLogs);

        if (result.flee === "success" && result.playerAction.type === "flee_success") {
            dispatchAnimation({ type: "FLEE_SUCCEEDED" });
        }

        const targetPlayerId = actorId === player.id ? enemy.id : player.id;
        const playerDamageValue = result.attackedFirst === player.id ? damage : undefined;
        const enemyDamageValue = result.attackedFirst === enemy.id ? damage : undefined;
        const battleStateToUse = isFirstAttack
            ? updateIntermediateBattleState(actorId, result, player, enemy)
            : mergedBattleState;

        if (damage && damage > 0) {
            await animateAttackStep(targetPlayerId, playerDamageValue, enemyDamageValue, battleStateToUse);
        } else {
            setBattleState(battleStateToUse);
        }
    };

    const playAttackSequence = async (result: BattleAttackResponse, player: BattlePlayer, enemy: BattlePlayer) => {
        const { firstAttacker, secondAttacker, firstDamage, secondDamage, filteredNewLogs } = processAttackData(
            result,
            player,
            enemy
        );
        const mergedBattleState = mergeCombatLogs(result.battleState);

        await executeTurnAnimation(
            firstAttacker.id,
            firstDamage,
            filteredNewLogs,
            result,
            true,
            mergedBattleState,
            player,
            enemy
        );
        await delay(250);
        await executeTurnAnimation(
            secondAttacker.id,
            secondDamage,
            filteredNewLogs,
            result,
            false,
            mergedBattleState,
            player,
            enemy
        );

        dispatchAnimation({ type: "ATTACK_SEQUENCE_COMPLETED" });
    };

    const handleAction = async (action: BattleAction, player: BattlePlayer, enemy: BattlePlayer) => {
        if (!battleState || loading || animationState.isAnimating) return;

        setLoading(true);
        dispatchAnimation({ type: "ATTACK_SEQUENCE_STARTED" });

        try {
            const result = await startAttackMutation.mutateAsync({ action });
            await playAttackSequence(result, player, enemy);
        } catch (error) {
            console.error("Attack failed:", error);
            dispatchAnimation({ type: "ATTACK_SEQUENCE_COMPLETED" });
        } finally {
            setLoading(false);
        }
    };

    const isLoadingState = isLoading || isBattleStatusLoading || (battleStatus && !battleState);
    const hasError = battleStatusError || currentUserError;
    const isMissingData = !currentUser || !battleState;

    useEffect(() => {
        const initBattle = () => {
            if (battleStatus) {
                setBattleState(battleStatus);
                if (battleStatus.combatLog) {
                    setCombatLogs(battleStatus.combatLog);
                }
            } else {
                console.error("No active battle found");
            }
        };

        if (isFetchedAfterMount && !battleState && battleStatus) {
            initBattle();
        }
    }, [battleStatus, isFetchedAfterMount, battleState]);

    // Early returns for loading and error states
    if (isLoadingState) {
        return <BattleLoadingScreen isLoading error={null} />;
    }

    if (hasError) {
        return <BattleLoadingScreen isLoading={false} error={hasError} />;
    }

    if (isMissingData) {
        return <BattleLoadingScreen isLoading={false} error={new Error("Missing battle data")} />;
    }

    const battleParticipants = resolveBattleParticipants(battleState, String(currentUser.id));

    if (!battleParticipants?.player || !battleParticipants?.enemy) {
        return <BattleLoadingScreen isLoading={false} error={new Error("Missing player or enemy data")} />;
    }

    const { player, enemy } = battleParticipants;

    return (
        <div className="min-h-screen bg-gray-900 p-6 relative">
            <div className="mx-auto max-w-4xl space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 relative">
                    <PlayerStatus
                        userInfo={player}
                        isCurrentTurn={animationState.isAnimating && animationState.attackedPlayer !== player.id}
                        isBeingAttacked={animationState.attackedPlayer === player.id}
                        damageReceived={animationState.enemyDamage}
                        healAmount={healAmount}
                        side="left"
                    />
                    <PlayerStatus
                        userInfo={enemy}
                        isCurrentTurn={animationState.isAnimating && animationState.attackedPlayer !== enemy.id}
                        isBeingAttacked={animationState.attackedPlayer === enemy.id}
                        damageReceived={animationState.playerDamage}
                        side="right"
                    />
                </div>

                <div className="mt-0 mb-4 h-2 w-full rounded-full bg-gray-700 blur-xs md:mb-1 md:hidden 2xl:mt-2 2xl:mb-4 2xl:block"></div>

                <div className="md:-mt-8 md:scale-[0.85] 2xl:mt-0 2xl:scale-100">
                    {player.currentHealth <= 0 ? (
                        <DeathPanel />
                    ) : enemy.currentHealth <= 0 || animationState.fleeSuccess ? (
                        <VictoryPanel
                            enemy={enemy.username}
                            isNPC={enemy.userType !== "player"}
                            battleType={battleState?.battleType}
                            flee={animationState.fleeSuccess}
                        />
                    ) : (
                        <BattlePanel
                            handleAction={(action: BattleAction) => handleAction(action, player, enemy)}
                            isButtonDisabled={loading || animationState.isAnimating}
                            player={player}
                            currentUser={{
                                ...currentUser,
                                statusEffects: player.statusEffects,
                                currentStamina: player.currentStamina,
                            }}
                        />
                    )}
                </div>

                {/* Combat Log */}
                <BattleLog logs={combatLogs} player={player} enemy={enemy} />
            </div>
        </div>
    );
}

export default Battle;
