import SortAndFilterControls, { type FilterGroup } from "@/components/SortAndFilterControls";
import type { QuestFilterOption, QuestSortOption, SortDirection } from "../hooks/useQuestSortAndFilter";
import type { QuestGiver } from "@/types/quest";

interface QuestSortAndFilterControlsProps {
    sortOption: QuestSortOption;
    setSortOption: (option: QuestSortOption) => void;
    sortDirection: SortDirection;
    setSortDirection: (direction: SortDirection) => void;
    filterOptions: QuestFilterOption;
    setFilterOptions: (options: QuestFilterOption) => void;
    questGivers: QuestGiver[];
    activeTab: string;
    showSearch?: boolean;
    hasActiveFilters?: boolean;
    clearFilters?: () => void;
}

export default function QuestSortAndFilterControls({
    sortOption,
    setSortOption,
    sortDirection,
    setSortDirection,
    filterOptions,
    setFilterOptions,
    questGivers,
    activeTab,
    showSearch = false,
    hasActiveFilters,
    clearFilters,
}: QuestSortAndFilterControlsProps) {
    // Sort option labels
    const sortOptions: Record<QuestSortOption, string> = {
        level: "Level",
        name: "Name",
        Yen_reward: "Yen Reward",
        XP_reward: "XP Reward",
        REP_reward: "REP Reward",
        quest_giver: "Quest Giver",
    };

    // Filter groups configuration
    const filterGroups: FilterGroup[] = [
        {
            label: "",
            fields: [
                {
                    key: "status",
                    label: "Status",
                    type: "select",
                    disabled: activeTab === "complete",
                    options: [
                        { value: "all", label: activeTab === "complete" ? "Completed" : "All" },
                        { value: "available", label: "New" },
                        { value: "in_progress", label: "In Progress" },
                        { value: "ready_to_complete", label: "Ready" },
                    ],
                },
                {
                    key: "questGiverId",
                    label: "Quest Giver",
                    type: "select",
                    options: [
                        { value: "", label: "All Givers" },
                        ...questGivers
                            .filter((giver) => giver.shopType !== "gang")
                            .map((giver) => ({
                                value: giver.id,
                                label: giver.name,
                            })),
                    ],
                },
                {
                    key: "location",
                    label: "Location",
                    type: "select",
                    options: [
                        { value: "any", label: "Any" },
                        { value: "church", label: "Church" },
                        { value: "shrine", label: "Shrine" },
                        { value: "mall", label: "Mall" },
                        { value: "alley", label: "Alley" },
                        { value: "school", label: "School" },
                        { value: "sewers", label: "Sewers" },
                    ],
                },
            ],
        },
        {
            label: "Level Range",
            fields: [
                {
                    key: "minLevel",
                    label: "Min Level",
                    type: "number",
                    placeholder: "Min",
                    min: 1,
                },
                {
                    key: "maxLevel",
                    label: "Max Level", 
                    type: "number",
                    placeholder: "Max",
                    min: 1,
                },
            ],
        },
        {
            label: "Reward Filters",
            fields: [
                {
                    key: "hasItemRewards",
                    label: "Has Items",
                    type: "checkbox",
                },
                {
                    key: "hasTalentPointRewards",
                    label: "Has Talent Points",
                    type: "checkbox",
                },
            ],
        },
    ];

    return (
        <SortAndFilterControls
            sortOption={sortOption}
            setSortOption={setSortOption}
            sortDirection={sortDirection}
            setSortDirection={setSortDirection}
            sortOptions={sortOptions}
            filterOptions={filterOptions}
            setFilterOptions={setFilterOptions}
            filterGroups={filterGroups}
            showSearch={showSearch}
            searchPlaceholder="Search quests..."
            hasActiveFilters={hasActiveFilters}
            clearFilters={clearFilters}
        />
    );
}
