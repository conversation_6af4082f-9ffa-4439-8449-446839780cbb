import { cn } from "@/lib/utils";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useUserList from "../features/facultylist/api/useUserList";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import Spinner from "@/components/Spinners/Spinner";
import { useState } from "react";
import { DisplayGangIcon } from "@/components/DisplayGangIcon";
import { Link } from "react-router-dom";
import Pagination from "@/components/Tables/Pagination";

function FacultyList() {
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(50);
    const { isLoading, error, data } = useUserList({ page, limit });
    const { data: currentUser } = useFetchCurrentUser();

    if (error)
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="bg-gradient-to-br from-red-900/40 via-black/60 to-red-800/40 p-6 rounded-xl border-2 border-red-500/50 shadow-lg">
                    <p className="text-red-300 font-medium">An error has occurred: {error.message}</p>
                </div>
            </div>
        );

    if (isLoading)
        return (
            <div className="min-h-screen flex items-center justify-center">
                <Spinner />
            </div>
        );

    const users = data?.users || [];
    const totalPages = data?.totalPages || 1;
    const currentPage = data?.currentPage || 1;
    const hasNextPage = data?.hasNextPage || false;
    const hasPreviousPage = data?.hasPreviousPage || false;
    const totalCount = data?.totalCount || 0;

    const handleNextPage = () => {
        if (hasNextPage) {
            setPage(page + 1);
        }
    };

    const handlePreviousPage = () => {
        if (hasPreviousPage) {
            setPage(page - 1);
        }
    };

    const handleLimitChange = (newLimit: number) => {
        setLimit(newLimit);
        setPage(1); // Reset to first page when changing limit
    };

    const getUserTypeStyles = (userType: string) => {
        switch (userType.toLowerCase()) {
            case "admin":
                return {
                    badge: "bg-gradient-to-r from-red-600 via-red-500 to-red-600 border-red-400/80 text-white",
                    glow: "shadow-lg shadow-red-500/30",
                    icon: "⚔️",
                };
            case "prefect":
                return {
                    badge: "bg-gradient-to-r from-yellow-600 via-yellow-500 to-orange-600 border-yellow-400/80 text-white",
                    glow: "shadow-lg shadow-yellow-500/30",
                    icon: "🛡️",
                };
            default:
                return {
                    badge: "bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 border-slate-500/60 text-slate-200",
                    glow: "shadow-lg shadow-slate-500/20",
                    icon: "📚",
                };
        }
    };

    return (
        <div className="min-h-screen">
            {/* Main container */}
            <div className="relative z-10 container mx-auto md:p-4 py-8 max-w-7xl">
                {users.length === 0 ? (
                    <div className="flex items-center justify-center py-16">
                        <div className="bg-gradient-to-br from-slate-800/60 via-slate-900/80 to-black/90 p-8 rounded-xl border border-slate-600/50 shadow-lg">
                            <p className="text-slate-400 text-lg font-medium">Error loading users</p>
                        </div>
                    </div>
                ) : (
                    <>
                        {/* List Container */}
                        <div className="bg-gradient-to-br from-slate-800/90 via-blue-900/50 to-slate-800/90 md:rounded-xl border md:border-2 border-blue-400/25 shadow-2xl shadow-blue-500/20 overflow-hidden mb-8 backdrop-blur-sm">
                            {/* List Header - Hidden on mobile */}
                            <div className="hidden md:block bg-gradient-to-r from-blue-900/40 via-blue-800/30 to-blue-900/40 border-b border-blue-400/25 px-6 py-3">
                                <div className="grid grid-cols-12 gap-4 items-center text-xs font-bold uppercase tracking-wider text-blue-300">
                                    <div className="col-span-5">User</div>
                                    <div className="col-span-2">Level</div>
                                    <div className="col-span-3">Gang</div>
                                    <div className="col-span-2">Type</div>
                                </div>
                            </div>

                            {/* User List */}
                            <div className="divide-y divide-blue-400/25">
                                {users.map((user: any, index: number) => {
                                    const typeStyles = getUserTypeStyles(user.userType);
                                    const isCurrentUser = currentUser?.id === user.id;

                                    return (
                                        <Link
                                            key={user.id}
                                            to={`/profile/${user.id}`}
                                            className={cn(
                                                "group relative transition-all duration-300 block",
                                                "hover:bg-gradient-to-r hover:from-blue-800/40 hover:via-blue-900/20 hover:to-blue-800/40",
                                                "hover:shadow-lg hover:shadow-blue-500/10",
                                                "cursor-pointer",
                                                index % 2 === 0 ? "bg-slate-900/20" : "bg-slate-900/50"
                                            )}
                                        >
                                            {/* Current user indicator bar */}
                                            {isCurrentUser && (
                                                <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-yellow-500 via-yellow-400 to-yellow-500" />
                                            )}

                                            {/* Mobile Layout - Stacked */}
                                            <div className="md:hidden px-4 py-4">
                                                <div className="flex items-start gap-3">
                                                    {/* Avatar */}
                                                    <div className="relative flex-shrink-0">
                                                        <div className="relative rounded-lg p-0.5 border-2 border-blue-400/80 shadow-xl shadow-blue-500/20 group-hover:border-blue-300 group-hover:shadow-blue-400/30 transition-all duration-300">
                                                            <DisplayAvatar
                                                                src={user}
                                                                className="size-14 rounded-lg object-cover"
                                                            />
                                                        </div>
                                                    </div>

                                                    {/* User Info */}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-start justify-between mb-1">
                                                            <div className="flex-1 mr-2">
                                                                {/* Username with inline ID */}
                                                                <div className="flex items-center gap-2 flex-wrap">
                                                                    <span className="text-[10px] text-slate-400 bg-black/40 px-1.5 py-0.5 rounded border border-slate-700/60">
                                                                        #{user.id}
                                                                    </span>
                                                                    <h3 className="font-black text-white text-base uppercase tracking-wider drop-shadow-md group-hover:text-blue-100 transition-colors duration-300">
                                                                        {user.username}
                                                                    </h3>
                                                                </div>

                                                                {/* Level and Gang below username */}
                                                                <div className="flex items-center gap-2 flex-wrap mt-2">
                                                                    {/* Level */}
                                                                    <div className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 px-2 py-0.5 rounded border border-blue-400/60 shadow-md inline-flex">
                                                                        <p className="text-[10px] font-bold text-white uppercase">
                                                                            LVL {user.level}
                                                                        </p>
                                                                    </div>

                                                                    {/* Gang */}
                                                                    {user.gang ? (
                                                                        <div className="flex items-center gap-1 bg-gradient-to-br from-indigo-700 to-indigo-800 py-0.5 px-1.5 rounded border border-indigo-500/80 shadow-md">
                                                                            {user.gang.avatar && (
                                                                                <DisplayGangIcon
                                                                                    src={user.gang}
                                                                                    className="size-5 rounded object-cover"
                                                                                />
                                                                            )}
                                                                            <p className="text-[10px] font-bold text-white">
                                                                                {user.gang.name}
                                                                            </p>
                                                                        </div>
                                                                    ) : (
                                                                        <p className="text-[10px] text-slate-400 italic">
                                                                            No Gang
                                                                        </p>
                                                                    )}
                                                                </div>
                                                            </div>

                                                            {/* Type Badge */}
                                                            <div
                                                                className={cn(
                                                                    "px-2 py-1 rounded border shadow-md inline-flex items-center gap-1 flex-shrink-0",
                                                                    typeStyles.badge,
                                                                    typeStyles.glow
                                                                )}
                                                            >
                                                                <span className="text-[10px]">{typeStyles.icon}</span>
                                                                <p className="text-[10px] font-bold uppercase">
                                                                    {capitaliseFirstLetter(user.userType)}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Desktop Layout - Grid */}
                                            <div className="hidden md:grid grid-cols-12 gap-4 items-center px-6 py-4">
                                                {/* Combined User Column (Avatar + Name + ID) */}
                                                <div className="col-span-5">
                                                    <div className="flex items-center gap-3">
                                                        {/* Avatar */}
                                                        <div className="relative flex-shrink-0">
                                                            <div className="relative rounded-lg p-0.5 border-2 border-blue-400/80 shadow-xl shadow-blue-500/20 group-hover:border-blue-300 group-hover:shadow-blue-400/30 transition-all duration-300">
                                                                <DisplayAvatar
                                                                    src={user}
                                                                    className="size-12 rounded-lg object-cover"
                                                                />
                                                            </div>
                                                        </div>

                                                        {/* Name and ID */}
                                                        <div className="flex-1 min-w-0">
                                                            <div className="flex items-center gap-2 flex-wrap">
                                                                <span className="text-[11px] text-slate-400 bg-black/40 px-2 py-0.5 rounded border border-slate-700/60">
                                                                    #{user.id}
                                                                </span>
                                                                <h3 className="font-black text-white text-base uppercase tracking-wider truncate drop-shadow-md group-hover:text-blue-100 transition-colors duration-300">
                                                                    {user.username}
                                                                </h3>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Level Column */}
                                                <div className="col-span-2">
                                                    <div className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 px-3 py-1.5 rounded border border-blue-400/60 shadow-md inline-flex">
                                                        <p className="text-xs font-bold text-white uppercase">
                                                            LVL {user.level}
                                                        </p>
                                                    </div>
                                                </div>

                                                {/* Gang Column */}
                                                <div className="col-span-3">
                                                    {user.gang ? (
                                                        <div className="flex items-center gap-2 bg-gradient-to-br from-indigo-700 to-indigo-800 p-0.5 rounded border border-indigo-500/80 shadow-md w-fit px-2.5">
                                                            {user.gang.avatar && (
                                                                <div className="relative">
                                                                    <DisplayGangIcon
                                                                        src={user.gang}
                                                                        className="size-8 rounded object-cover"
                                                                    />
                                                                </div>
                                                            )}
                                                            <div className="flex-1 min-w-0">
                                                                <p className="text-sm font-bold text-white truncate">
                                                                    {user.gang.name}
                                                                </p>
                                                                <p className="text-[10px] text-slate-400">
                                                                    Active Member
                                                                </p>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <p className="text-xs text-slate-400 italic">
                                                            No Gang Affiliation
                                                        </p>
                                                    )}
                                                </div>

                                                {/* Type Column */}
                                                <div className="col-span-2">
                                                    <div
                                                        className={cn(
                                                            "px-3 py-1.5 rounded border shadow-md inline-flex items-center gap-1",
                                                            typeStyles.badge,
                                                            typeStyles.glow
                                                        )}
                                                    >
                                                        <span className="text-xs">{typeStyles.icon}</span>
                                                        <p className="text-xs font-bold uppercase">
                                                            {capitaliseFirstLetter(user.userType)}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </Link>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Pagination Controls */}
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            totalCount={totalCount}
                            limit={limit}
                            hasNextPage={hasNextPage}
                            hasPreviousPage={hasPreviousPage}
                            onPageChange={setPage}
                            onLimitChange={handleLimitChange}
                            itemName="Users"
                        />
                    </>
                )}
            </div>
        </div>
    );
}

export default FacultyList;
