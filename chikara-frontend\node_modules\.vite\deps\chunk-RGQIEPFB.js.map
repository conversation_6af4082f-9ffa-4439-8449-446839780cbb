{"version": 3, "sources": ["../../../../node_modules/.bun/@orpc+standard-server@1.8.0/node_modules/@orpc/standard-server/dist/index.mjs"], "sourcesContent": ["import { isTypescriptObject, AsyncIteratorClass, tryDecodeURIComponent, toArray, once, isAsyncIteratorObject, replicateAsyncIterator } from '@orpc/shared';\n\nclass EventEncoderError extends TypeError {\n}\nclass EventDecoderError extends TypeError {\n}\nclass ErrorEvent extends Error {\n  data;\n  constructor(options) {\n    super(options?.message ?? \"An error event was received\", options);\n    this.data = options?.data;\n  }\n}\n\nfunction decodeEventMessage(encoded) {\n  const lines = encoded.replace(/\\n+$/, \"\").split(/\\n/);\n  const message = {\n    data: void 0,\n    event: void 0,\n    id: void 0,\n    retry: void 0,\n    comments: []\n  };\n  for (const line of lines) {\n    const index = line.indexOf(\":\");\n    const key = index === -1 ? line : line.slice(0, index);\n    const value = index === -1 ? \"\" : line.slice(index + 1).replace(/^\\s/, \"\");\n    if (index === 0) {\n      message.comments.push(value);\n    } else if (key === \"data\") {\n      message.data ??= \"\";\n      message.data += `${value}\n`;\n    } else if (key === \"event\") {\n      message.event = value;\n    } else if (key === \"id\") {\n      message.id = value;\n    } else if (key === \"retry\") {\n      const maybeInteger = Number.parseInt(value);\n      if (Number.isInteger(maybeInteger) && maybeInteger >= 0 && maybeInteger.toString() === value) {\n        message.retry = maybeInteger;\n      }\n    }\n  }\n  message.data = message.data?.replace(/\\n$/, \"\");\n  return message;\n}\nclass EventDecoder {\n  constructor(options = {}) {\n    this.options = options;\n  }\n  incomplete = \"\";\n  feed(chunk) {\n    this.incomplete += chunk;\n    const lastCompleteIndex = this.incomplete.lastIndexOf(\"\\n\\n\");\n    if (lastCompleteIndex === -1) {\n      return;\n    }\n    const completes = this.incomplete.slice(0, lastCompleteIndex).split(/\\n\\n/);\n    this.incomplete = this.incomplete.slice(lastCompleteIndex + 2);\n    for (const encoded of completes) {\n      const message = decodeEventMessage(`${encoded}\n\n`);\n      if (this.options.onEvent) {\n        this.options.onEvent(message);\n      }\n    }\n    this.incomplete = \"\";\n  }\n  end() {\n    if (this.incomplete) {\n      throw new EventDecoderError(\"Event Iterator ended before complete\");\n    }\n  }\n}\nclass EventDecoderStream extends TransformStream {\n  constructor() {\n    let decoder;\n    super({\n      start(controller) {\n        decoder = new EventDecoder({\n          onEvent: (event) => {\n            controller.enqueue(event);\n          }\n        });\n      },\n      transform(chunk) {\n        decoder.feed(chunk);\n      },\n      flush() {\n        decoder.end();\n      }\n    });\n  }\n}\n\nfunction assertEventId(id) {\n  if (id.includes(\"\\n\")) {\n    throw new EventEncoderError(\"Event's id must not contain a newline character\");\n  }\n}\nfunction assertEventName(event) {\n  if (event.includes(\"\\n\")) {\n    throw new EventEncoderError(\"Event's event must not contain a newline character\");\n  }\n}\nfunction assertEventRetry(retry) {\n  if (!Number.isInteger(retry) || retry < 0) {\n    throw new EventEncoderError(\"Event's retry must be a integer and >= 0\");\n  }\n}\nfunction assertEventComment(comment) {\n  if (comment.includes(\"\\n\")) {\n    throw new EventEncoderError(\"Event's comment must not contain a newline character\");\n  }\n}\nfunction encodeEventData(data) {\n  const lines = data?.split(/\\n/) ?? [];\n  let output = \"\";\n  for (const line of lines) {\n    output += `data: ${line}\n`;\n  }\n  return output;\n}\nfunction encodeEventComments(comments) {\n  let output = \"\";\n  for (const comment of comments ?? []) {\n    assertEventComment(comment);\n    output += `: ${comment}\n`;\n  }\n  return output;\n}\nfunction encodeEventMessage(message) {\n  let output = \"\";\n  output += encodeEventComments(message.comments);\n  if (message.event !== void 0) {\n    assertEventName(message.event);\n    output += `event: ${message.event}\n`;\n  }\n  if (message.retry !== void 0) {\n    assertEventRetry(message.retry);\n    output += `retry: ${message.retry}\n`;\n  }\n  if (message.id !== void 0) {\n    assertEventId(message.id);\n    output += `id: ${message.id}\n`;\n  }\n  output += encodeEventData(message.data);\n  output += \"\\n\";\n  return output;\n}\n\nconst EVENT_SOURCE_META_SYMBOL = Symbol(\"ORPC_EVENT_SOURCE_META\");\nfunction withEventMeta(container, meta) {\n  if (meta.id !== void 0) {\n    assertEventId(meta.id);\n  }\n  if (meta.retry !== void 0) {\n    assertEventRetry(meta.retry);\n  }\n  if (meta.comments !== void 0) {\n    for (const comment of meta.comments) {\n      assertEventComment(comment);\n    }\n  }\n  return new Proxy(container, {\n    get(target, prop, receiver) {\n      if (prop === EVENT_SOURCE_META_SYMBOL) {\n        return meta;\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  });\n}\nfunction getEventMeta(container) {\n  return isTypescriptObject(container) ? Reflect.get(container, EVENT_SOURCE_META_SYMBOL) : void 0;\n}\n\nclass HibernationEventIterator extends AsyncIteratorClass {\n  /**\n   * this property is not transferred to the client, so it should be optional for type safety\n   */\n  hibernationCallback;\n  constructor(hibernationCallback) {\n    super(async () => {\n      throw new Error(\"Cannot iterate over hibernating iterator directly\");\n    }, async (reason) => {\n      if (reason !== \"next\") {\n        throw new Error(\"Cannot cleanup hibernating iterator directly\");\n      }\n    });\n    this.hibernationCallback = hibernationCallback;\n  }\n}\n\nfunction generateContentDisposition(filename) {\n  const escapedFileName = filename.replace(/\"/g, '\\\\\"');\n  const encodedFilenameStar = encodeURIComponent(filename).replace(/['()*]/g, (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`).replace(/%(7C|60|5E)/g, (str, hex) => String.fromCharCode(Number.parseInt(hex, 16)));\n  return `inline; filename=\"${escapedFileName}\"; filename*=utf-8''${encodedFilenameStar}`;\n}\nfunction getFilenameFromContentDisposition(contentDisposition) {\n  const encodedFilenameStarMatch = contentDisposition.match(/filename\\*=(UTF-8'')?([^;]*)/i);\n  if (encodedFilenameStarMatch && typeof encodedFilenameStarMatch[2] === \"string\") {\n    return tryDecodeURIComponent(encodedFilenameStarMatch[2]);\n  }\n  const encodedFilenameMatch = contentDisposition.match(/filename=\"((?:\\\\\"|[^\"])*)\"/i);\n  if (encodedFilenameMatch && typeof encodedFilenameMatch[1] === \"string\") {\n    return encodedFilenameMatch[1].replace(/\\\\\"/g, '\"');\n  }\n}\nfunction mergeStandardHeaders(a, b) {\n  const merged = { ...a };\n  for (const key in b) {\n    if (Array.isArray(b[key])) {\n      merged[key] = [...toArray(merged[key]), ...b[key]];\n    } else if (b[key] !== void 0) {\n      if (Array.isArray(merged[key])) {\n        merged[key] = [...merged[key], b[key]];\n      } else if (merged[key] !== void 0) {\n        merged[key] = [merged[key], b[key]];\n      } else {\n        merged[key] = b[key];\n      }\n    }\n  }\n  return merged;\n}\nfunction flattenHeader(header) {\n  if (typeof header === \"string\" || header === void 0) {\n    return header;\n  }\n  if (header.length === 0) {\n    return void 0;\n  }\n  return header.join(\", \");\n}\nfunction replicateStandardLazyResponse(response, count) {\n  const replicated = [];\n  let bodyPromise;\n  let replicatedAsyncIteratorObjects;\n  for (let i = 0; i < count; i++) {\n    replicated.push({\n      ...response,\n      body: once(async () => {\n        if (replicatedAsyncIteratorObjects) {\n          return replicatedAsyncIteratorObjects.shift();\n        }\n        const body = await (bodyPromise ??= response.body());\n        if (!isAsyncIteratorObject(body)) {\n          return body;\n        }\n        replicatedAsyncIteratorObjects = replicateAsyncIterator(body, count);\n        return replicatedAsyncIteratorObjects.shift();\n      })\n    });\n  }\n  return replicated;\n}\nfunction isEventIteratorHeaders(headers) {\n  return Boolean(flattenHeader(headers[\"content-type\"])?.startsWith(\"text/event-stream\") && flattenHeader(headers[\"content-disposition\"]) === void 0);\n}\n\nexport { ErrorEvent, EventDecoder, EventDecoderError, EventDecoderStream, EventEncoderError, HibernationEventIterator, assertEventComment, assertEventId, assertEventName, assertEventRetry, decodeEventMessage, encodeEventComments, encodeEventData, encodeEventMessage, flattenHeader, generateContentDisposition, getEventMeta, getFilenameFromContentDisposition, isEventIteratorHeaders, mergeStandardHeaders, replicateStandardLazyResponse, withEventMeta };\n"], "mappings": ";;;;;;;;;;AAEA,IAAM,oBAAN,cAAgC,UAAU;AAC1C;AACA,IAAM,oBAAN,cAAgC,UAAU;AAC1C;AACA,IAAM,aAAN,cAAyB,MAAM;AAAA,EAC7B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,SAAS,WAAW,+BAA+B,OAAO;AAChE,SAAK,OAAO,SAAS;AAAA,EACvB;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,QAAM,QAAQ,QAAQ,QAAQ,QAAQ,EAAE,EAAE,MAAM,IAAI;AACpD,QAAM,UAAU;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,UAAU,CAAC;AAAA,EACb;AACA,aAAW,QAAQ,OAAO;AACxB,UAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,UAAM,MAAM,UAAU,KAAK,OAAO,KAAK,MAAM,GAAG,KAAK;AACrD,UAAM,QAAQ,UAAU,KAAK,KAAK,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,OAAO,EAAE;AACzE,QAAI,UAAU,GAAG;AACf,cAAQ,SAAS,KAAK,KAAK;AAAA,IAC7B,WAAW,QAAQ,QAAQ;AACzB,cAAQ,SAAS;AACjB,cAAQ,QAAQ,GAAG,KAAK;AAAA;AAAA,IAE1B,WAAW,QAAQ,SAAS;AAC1B,cAAQ,QAAQ;AAAA,IAClB,WAAW,QAAQ,MAAM;AACvB,cAAQ,KAAK;AAAA,IACf,WAAW,QAAQ,SAAS;AAC1B,YAAM,eAAe,OAAO,SAAS,KAAK;AAC1C,UAAI,OAAO,UAAU,YAAY,KAAK,gBAAgB,KAAK,aAAa,SAAS,MAAM,OAAO;AAC5F,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,UAAQ,OAAO,QAAQ,MAAM,QAAQ,OAAO,EAAE;AAC9C,SAAO;AACT;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa;AAAA,EACb,KAAK,OAAO;AACV,SAAK,cAAc;AACnB,UAAM,oBAAoB,KAAK,WAAW,YAAY,MAAM;AAC5D,QAAI,sBAAsB,IAAI;AAC5B;AAAA,IACF;AACA,UAAM,YAAY,KAAK,WAAW,MAAM,GAAG,iBAAiB,EAAE,MAAM,MAAM;AAC1E,SAAK,aAAa,KAAK,WAAW,MAAM,oBAAoB,CAAC;AAC7D,eAAW,WAAW,WAAW;AAC/B,YAAM,UAAU,mBAAmB,GAAG,OAAO;AAAA;AAAA,CAElD;AACK,UAAI,KAAK,QAAQ,SAAS;AACxB,aAAK,QAAQ,QAAQ,OAAO;AAAA,MAC9B;AAAA,IACF;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,MAAM;AACJ,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACpE;AAAA,EACF;AACF;AACA,IAAM,qBAAN,cAAiC,gBAAgB;AAAA,EAC/C,cAAc;AACZ,QAAI;AACJ,UAAM;AAAA,MACJ,MAAM,YAAY;AAChB,kBAAU,IAAI,aAAa;AAAA,UACzB,SAAS,CAAC,UAAU;AAClB,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,UAAU,OAAO;AACf,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAAA,MACA,QAAQ;AACN,gBAAQ,IAAI;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,cAAc,IAAI;AACzB,MAAI,GAAG,SAAS,IAAI,GAAG;AACrB,UAAM,IAAI,kBAAkB,iDAAiD;AAAA,EAC/E;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,MAAM,SAAS,IAAI,GAAG;AACxB,UAAM,IAAI,kBAAkB,oDAAoD;AAAA,EAClF;AACF;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,CAAC,OAAO,UAAU,KAAK,KAAK,QAAQ,GAAG;AACzC,UAAM,IAAI,kBAAkB,0CAA0C;AAAA,EACxE;AACF;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,QAAQ,SAAS,IAAI,GAAG;AAC1B,UAAM,IAAI,kBAAkB,sDAAsD;AAAA,EACpF;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,QAAM,QAAQ,MAAM,MAAM,IAAI,KAAK,CAAC;AACpC,MAAI,SAAS;AACb,aAAW,QAAQ,OAAO;AACxB,cAAU,SAAS,IAAI;AAAA;AAAA,EAEzB;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,UAAU;AACrC,MAAI,SAAS;AACb,aAAW,WAAW,YAAY,CAAC,GAAG;AACpC,uBAAmB,OAAO;AAC1B,cAAU,KAAK,OAAO;AAAA;AAAA,EAExB;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,SAAS;AACb,YAAU,oBAAoB,QAAQ,QAAQ;AAC9C,MAAI,QAAQ,UAAU,QAAQ;AAC5B,oBAAgB,QAAQ,KAAK;AAC7B,cAAU,UAAU,QAAQ,KAAK;AAAA;AAAA,EAEnC;AACA,MAAI,QAAQ,UAAU,QAAQ;AAC5B,qBAAiB,QAAQ,KAAK;AAC9B,cAAU,UAAU,QAAQ,KAAK;AAAA;AAAA,EAEnC;AACA,MAAI,QAAQ,OAAO,QAAQ;AACzB,kBAAc,QAAQ,EAAE;AACxB,cAAU,OAAO,QAAQ,EAAE;AAAA;AAAA,EAE7B;AACA,YAAU,gBAAgB,QAAQ,IAAI;AACtC,YAAU;AACV,SAAO;AACT;AAEA,IAAM,2BAA2B,OAAO,wBAAwB;AAChE,SAAS,cAAc,WAAW,MAAM;AACtC,MAAI,KAAK,OAAO,QAAQ;AACtB,kBAAc,KAAK,EAAE;AAAA,EACvB;AACA,MAAI,KAAK,UAAU,QAAQ;AACzB,qBAAiB,KAAK,KAAK;AAAA,EAC7B;AACA,MAAI,KAAK,aAAa,QAAQ;AAC5B,eAAW,WAAW,KAAK,UAAU;AACnC,yBAAmB,OAAO;AAAA,IAC5B;AAAA,EACF;AACA,SAAO,IAAI,MAAM,WAAW;AAAA,IAC1B,IAAI,QAAQ,MAAM,UAAU;AAC1B,UAAI,SAAS,0BAA0B;AACrC,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAAA,IAC3C;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,mBAAmB,SAAS,IAAI,QAAQ,IAAI,WAAW,wBAAwB,IAAI;AAC5F;AAmBA,SAAS,2BAA2B,UAAU;AAC5C,QAAM,kBAAkB,SAAS,QAAQ,MAAM,KAAK;AACpD,QAAM,sBAAsB,mBAAmB,QAAQ,EAAE,QAAQ,WAAW,CAAC,MAAM,IAAI,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,QAAQ,gBAAgB,CAAC,KAAK,QAAQ,OAAO,aAAa,OAAO,SAAS,KAAK,EAAE,CAAC,CAAC;AACxN,SAAO,qBAAqB,eAAe,uBAAuB,mBAAmB;AACvF;AACA,SAAS,kCAAkC,oBAAoB;AAC7D,QAAM,2BAA2B,mBAAmB,MAAM,+BAA+B;AACzF,MAAI,4BAA4B,OAAO,yBAAyB,CAAC,MAAM,UAAU;AAC/E,WAAO,sBAAsB,yBAAyB,CAAC,CAAC;AAAA,EAC1D;AACA,QAAM,uBAAuB,mBAAmB,MAAM,6BAA6B;AACnF,MAAI,wBAAwB,OAAO,qBAAqB,CAAC,MAAM,UAAU;AACvE,WAAO,qBAAqB,CAAC,EAAE,QAAQ,QAAQ,GAAG;AAAA,EACpD;AACF;AACA,SAAS,qBAAqB,GAAG,GAAG;AAClC,QAAM,SAAS,EAAE,GAAG,EAAE;AACtB,aAAW,OAAO,GAAG;AACnB,QAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,GAAG;AACzB,aAAO,GAAG,IAAI,CAAC,GAAG,QAAQ,OAAO,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,IACnD,WAAW,EAAE,GAAG,MAAM,QAAQ;AAC5B,UAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AAC9B,eAAO,GAAG,IAAI,CAAC,GAAG,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,MACvC,WAAW,OAAO,GAAG,MAAM,QAAQ;AACjC,eAAO,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,MACpC,OAAO;AACL,eAAO,GAAG,IAAI,EAAE,GAAG;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAUA,SAAS,8BAA8B,UAAU,OAAO;AACtD,QAAM,aAAa,CAAC;AACpB,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,eAAW,KAAK;AAAA,MACd,GAAG;AAAA,MACH,MAAM,KAAK,YAAY;AACrB,YAAI,gCAAgC;AAClC,iBAAO,+BAA+B,MAAM;AAAA,QAC9C;AACA,cAAM,OAAO,OAAO,gBAAgB,SAAS,KAAK;AAClD,YAAI,CAAC,sBAAsB,IAAI,GAAG;AAChC,iBAAO;AAAA,QACT;AACA,yCAAiC,uBAAuB,MAAM,KAAK;AACnE,eAAO,+BAA+B,MAAM;AAAA,MAC9C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": []}