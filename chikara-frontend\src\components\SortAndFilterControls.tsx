import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Arrow<PERSON>p<PERSON>own, X, ChevronDown, Search } from "lucide-react";
import { use<PERSON>emo, ReactNode } from "react";

export interface FilterField {
    key: string;
    label: string;
    type: "select" | "checkbox" | "number" | "text";
    options?: Array<{ value: string | number; label: string }>;
    placeholder?: string;
    min?: number;
    max?: number;
    disabled?: boolean;
    customRender?: (value: any, onChange: (value: any) => void) => ReactNode;
}

export interface FilterGroup {
    label: string;
    fields: FilterField[];
}

export interface SortAndFilterControlsProps<T extends string> {
    // Sort props
    sortOption: T;
    setSortOption: (option: T) => void;
    sortDirection: "asc" | "desc";
    setSortDirection: (direction: "asc" | "desc") => void;
    sortOptions: Record<T, string>;

    // Filter props
    filterOptions: Record<string, any>;
    setFilterOptions: (options: Record<string, any>) => void;
    filterGroups: FilterGroup[];

    // Search props
    showSearch?: boolean;
    searchPlaceholder?: string;

    // Clear filters
    clearFilters?: () => void;
    hasActiveFilters?: boolean;

    // Layout options
    className?: string;
}

export default function SortAndFilterControls<T extends string>({
    sortOption,
    setSortOption,
    sortDirection,
    setSortDirection,
    sortOptions,
    filterOptions,
    setFilterOptions,
    filterGroups,
    showSearch = false,
    searchPlaceholder = "Search...",
    clearFilters,
    hasActiveFilters = false,
    className = "",
}: SortAndFilterControlsProps<T>) {
    // Use provided hasActiveFilters or calculate automatically
    const computedHasActiveFilters = useMemo(() => {
        if (hasActiveFilters !== undefined) return hasActiveFilters;

        return Object.entries(filterOptions).some(([key, value]) => {
            if (key === "searchTerm") return value && value.trim() !== "";
            if (typeof value === "boolean") return value === true;
            if (typeof value === "string") return value !== "" && value !== "all" && value !== "any";
            if (typeof value === "number") return value !== undefined;
            return false;
        });
    }, [filterOptions, hasActiveFilters]);

    // Clear all filters
    const handleClearAllFilters = () => {
        if (clearFilters) {
            clearFilters();
        } else {
            // Default clear behavior
            const clearedFilters: Record<string, any> = {};
            filterGroups.forEach((group) => {
                group.fields.forEach((field) => {
                    if (field.type === "select") {
                        clearedFilters[field.key] = field.options?.[0]?.value || "all";
                    } else if (field.type === "checkbox") {
                        clearedFilters[field.key] = undefined;
                    } else if (field.type === "number") {
                        clearedFilters[field.key] = undefined;
                    } else {
                        clearedFilters[field.key] = "";
                    }
                });
            });
            if (showSearch) {
                clearedFilters.searchTerm = "";
            }
            setFilterOptions(clearedFilters);
        }
    };

    // Toggle sort direction
    const toggleSortDirection = () => {
        setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    };

    // Render filter field
    const renderFilterField = (field: FilterField) => {
        const value = filterOptions[field.key];

        if (field.customRender) {
            return field.customRender(value, (newValue) =>
                setFilterOptions({ ...filterOptions, [field.key]: newValue })
            );
        }

        const onChange = (newValue: any) => {
            setFilterOptions({ ...filterOptions, [field.key]: newValue });
        };

        switch (field.type) {
            case "select":
                return (
                    <select
                        disabled={field.disabled}
                        value={value || ""}
                        className="select select-sm w-full bg-base-100 border-neutral/30 focus:border-primary focus:outline-none py-0"
                        onChange={(e) => {
                            const val = e.target.value;
                            onChange(val === "" ? undefined : val);
                        }}
                    >
                        {field.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                );

            case "checkbox":
                return (
                    <label className="label cursor-pointer p-0 gap-2">
                        <input
                            type="checkbox"
                            checked={value === true}
                            className="checkbox checkbox-primary checkbox-xs"
                            onChange={(e) => onChange(e.target.checked || undefined)}
                        />
                        <span className="label-text text-xs">{field.label}</span>
                    </label>
                );

            case "number":
                return (
                    <input
                        type="number"
                        placeholder={field.placeholder}
                        value={value || ""}
                        min={field.min}
                        max={field.max}
                        className="input input-sm bg-base-100 border-neutral/30 focus:border-primary focus:outline-none w-full"
                        onChange={(e) => {
                            const val = e.target.value;
                            onChange(val ? parseInt(val) : undefined);
                        }}
                    />
                );

            case "text":
                return (
                    <input
                        type="text"
                        placeholder={field.placeholder}
                        value={value || ""}
                        className="input input-sm bg-base-100 border-neutral/30 focus:border-primary focus:outline-none w-full"
                        onChange={(e) => onChange(e.target.value)}
                    />
                );

            default:
                return null;
        }
    };

    // Render filter dropdown content
    const renderFilterDropdown = () => (
        <div
            tabIndex={0}
            className="dropdown-content z-[1] p-4 shadow-xl bg-base-300 border border-neutral/20 rounded-lg mt-1"
            style={{ width: "calc(100vw - 2rem)", maxWidth: "520px" }}
        >
            <div className="space-y-3">
                {filterGroups.map((group, groupIndex) => (
                    <div key={groupIndex}>
                        {group.label && <span className="label-text text-xs font-medium">{group.label}</span>}
                        <div
                            className={`grid gap-3 ${group.fields.length <= 3 ? `grid-cols-1 sm:grid-cols-${group.fields.length}` : "grid-cols-1 sm:grid-cols-3"}`}
                        >
                            {group.fields.map((field) => (
                                <div key={field.key} className="form-control">
                                    {field.type !== "checkbox" && (
                                        <label className="label pb-1 pt-0">
                                            <span className="label-text text-xs font-medium">{field.label}</span>
                                        </label>
                                    )}
                                    {renderFilterField(field)}
                                </div>
                            ))}
                        </div>
                        {groupIndex < filterGroups.length - 1 && (
                            <div className="border-t border-neutral/20 pt-2 mt-3" />
                        )}
                    </div>
                ))}
            </div>
        </div>
    );

    return (
        <div className={`space-y-4 ${className}`}>
            <div className="card bg-base-200 shadow-md">
                <div className="card-body p-3">
                    <div className={`flex items-center ${showSearch ? "justify-between" : "justify-between"}`}>
                        <div className="flex items-center gap-2 flex-1">
                            {/* Search Bar */}
                            {showSearch && (
                                <div className="relative min-w-0 flex-1 max-w-sm">
                                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-base-content/50" />
                                    <input
                                        type="text"
                                        placeholder={searchPlaceholder}
                                        value={filterOptions.searchTerm || ""}
                                        className="input input-sm w-full bg-base-100 border-neutral/30 focus:border-primary focus:outline-none pl-8"
                                        onChange={(e) =>
                                            setFilterOptions({
                                                ...filterOptions,
                                                searchTerm: e.target.value,
                                            })
                                        }
                                    />
                                    {filterOptions.searchTerm && filterOptions.searchTerm.trim() !== "" && (
                                        <button
                                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
                                            onClick={() =>
                                                setFilterOptions({
                                                    ...filterOptions,
                                                    searchTerm: "",
                                                })
                                            }
                                        >
                                            <X className="w-4 h-4" />
                                        </button>
                                    )}
                                </div>
                            )}

                            {/* Sort Split Button - only show on left side when search is not active */}
                            {!showSearch && (
                                <div className="join">
                                    <button className="btn btn-sm join-item gap-2" onClick={toggleSortDirection}>
                                        <ArrowUpDown className="w-4 h-4 hidden md:block" />
                                        <span>{sortOptions[sortOption]}</span>
                                        <span className="badge badge-sm badge-neutral">
                                            {sortDirection === "asc" ? "↑" : "↓"}
                                        </span>
                                    </button>
                                    <div className="dropdown dropdown-bottom">
                                        <button className="btn btn-sm join-item px-2" tabIndex={0}>
                                            <ChevronDown className="w-3 h-3" />
                                        </button>
                                        <ul
                                            tabIndex={0}
                                            className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-200 rounded-box w-52 mt-1 left-1/2 -translate-x-1/2 border border-base-300"
                                        >
                                            {Object.entries(sortOptions).map(([value, label]) => (
                                                <li key={value}>
                                                    <button
                                                        className={`justify-between ${sortOption === value ? "active" : ""}`}
                                                        onClick={() => {
                                                            setSortOption(value as T);
                                                            (document.activeElement as HTMLElement)?.blur();
                                                        }}
                                                    >
                                                        <span>{label}</span>
                                                        {sortOption === value && (
                                                            <span className="text-xs opacity-70">
                                                                {sortDirection === "asc" ? "↑" : "↓"}
                                                            </span>
                                                        )}
                                                    </button>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            {/* Clear All Button */}
                            {computedHasActiveFilters && (
                                <button className="btn btn-error btn-xs gap-1" onClick={handleClearAllFilters}>
                                    <X className="w-3 h-3" />
                                    Clear
                                </button>
                            )}

                            {/* Filter Dropdown */}
                            {filterGroups.length > 0 && (
                                <div className="dropdown dropdown-bottom dropdown-end">
                                    <button className="btn btn-sm gap-2 relative" tabIndex={0}>
                                        <SlidersHorizontal className="w-4 h-4" />
                                        Filters
                                        {computedHasActiveFilters && (
                                            <span className="absolute -top-1 -right-1 w-2 h-2 bg-warning rounded-full"></span>
                                        )}
                                        <ChevronDown className="w-3 h-3" />
                                    </button>
                                    {renderFilterDropdown()}
                                </div>
                            )}

                            {/* Sort Split Button - show on far right when search is active */}
                            {showSearch && (
                                <div className="join">
                                    <button className="btn btn-sm join-item gap-2" onClick={toggleSortDirection}>
                                        <ArrowUpDown className="w-4 h-4 hidden md:block" />
                                        <span>{sortOptions[sortOption]}</span>
                                        <span className="badge badge-sm badge-neutral">
                                            {sortDirection === "asc" ? "↑" : "↓"}
                                        </span>
                                    </button>
                                    <div className="dropdown dropdown-bottom">
                                        <button className="btn btn-sm join-item px-2" tabIndex={0}>
                                            <ChevronDown className="w-3 h-3" />
                                        </button>
                                        <ul
                                            tabIndex={0}
                                            className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-200 rounded-box w-52 mt-1 left-1/2 -translate-x-1/2 border border-base-300"
                                        >
                                            {Object.entries(sortOptions).map(([value, label]) => (
                                                <li key={value}>
                                                    <button
                                                        className={`justify-between ${sortOption === value ? "active" : ""}`}
                                                        onClick={() => {
                                                            setSortOption(value as T);
                                                            (document.activeElement as HTMLElement)?.blur();
                                                        }}
                                                    >
                                                        <span>{label}</span>
                                                        {sortOption === value && (
                                                            <span className="text-xs opacity-70">
                                                                {sortDirection === "asc" ? "↑" : "↓"}
                                                            </span>
                                                        )}
                                                    </button>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
