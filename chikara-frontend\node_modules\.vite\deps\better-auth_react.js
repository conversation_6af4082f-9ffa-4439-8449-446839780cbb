import {
  BetterAuthError,
  atom,
  createFetch,
  env,
  listen<PERSON><PERSON><PERSON>,
  useAuthQuery
} from "./chunk-APPR75T7.js";
import {
  require_react
} from "./chunk-JMQU4JNM.js";
import {
  __toESM
} from "./chunk-PR4QN5HX.js";

// ../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/shared/better-auth.CuS_eDdK.mjs
function checkHasPath(url) {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.pathname !== "/";
  } catch (error) {
    throw new BetterAuthError(
      `Invalid base URL: ${url}. Please provide a valid base URL.`
    );
  }
}
function withPath(url, path = "/api/auth") {
  const hasPath = checkHasPath(url);
  if (hasPath) {
    return url;
  }
  path = path.startsWith("/") ? path : `/${path}`;
  return `${url.replace(/\/+$/, "")}${path}`;
}
function getBaseURL(url, path, request) {
  if (url) {
    return withPath(url, path);
  }
  const fromEnv = env.BETTER_AUTH_URL || env.NEXT_PUBLIC_BETTER_AUTH_URL || env.PUBLIC_BETTER_AUTH_URL || env.NUXT_PUBLIC_BETTER_AUTH_URL || env.NUXT_PUBLIC_AUTH_URL || (env.BASE_URL !== "/" ? env.BASE_URL : void 0);
  if (fromEnv) {
    return withPath(fromEnv, path);
  }
  const fromRequest = request?.headers.get("x-forwarded-host");
  const fromRequestProto = request?.headers.get("x-forwarded-proto");
  if (fromRequest && fromRequestProto) {
    return withPath(`${fromRequestProto}://${fromRequest}`, path);
  }
  if (request) {
    const url2 = getOrigin(request.url);
    if (!url2) {
      throw new BetterAuthError(
        "Could not get origin from request. Please provide a valid base URL."
      );
    }
    return withPath(url2, path);
  }
  if (typeof window !== "undefined" && window.location) {
    return withPath(window.location.origin, path);
  }
  return void 0;
}
function getOrigin(url) {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.origin;
  } catch (error) {
    return null;
  }
}

// ../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs
var PROTO_POLLUTION_PATTERNS = {
  proto: /"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,
  constructor: /"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,
  protoShort: /"__proto__"\s*:/,
  constructorShort: /"constructor"\s*:/
};
var JSON_SIGNATURE = /^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;
var SPECIAL_VALUES = {
  true: true,
  false: false,
  null: null,
  undefined: void 0,
  nan: Number.NaN,
  infinity: Number.POSITIVE_INFINITY,
  "-infinity": Number.NEGATIVE_INFINITY
};
var ISO_DATE_REGEX = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/;
function isValidDate(date) {
  return date instanceof Date && !isNaN(date.getTime());
}
function parseISODate(value) {
  const match = ISO_DATE_REGEX.exec(value);
  if (!match) return null;
  const [
    ,
    year,
    month,
    day,
    hour,
    minute,
    second,
    ms,
    offsetSign,
    offsetHour,
    offsetMinute
  ] = match;
  let date = new Date(
    Date.UTC(
      parseInt(year, 10),
      parseInt(month, 10) - 1,
      parseInt(day, 10),
      parseInt(hour, 10),
      parseInt(minute, 10),
      parseInt(second, 10),
      ms ? parseInt(ms.padEnd(3, "0"), 10) : 0
    )
  );
  if (offsetSign) {
    const offset = (parseInt(offsetHour, 10) * 60 + parseInt(offsetMinute, 10)) * (offsetSign === "+" ? -1 : 1);
    date.setUTCMinutes(date.getUTCMinutes() + offset);
  }
  return isValidDate(date) ? date : null;
}
function betterJSONParse(value, options = {}) {
  const {
    strict = false,
    warnings = false,
    reviver,
    parseDates = true
  } = options;
  if (typeof value !== "string") {
    return value;
  }
  const trimmed = value.trim();
  if (trimmed[0] === '"' && trimmed.endsWith('"') && !trimmed.slice(1, -1).includes('"')) {
    return trimmed.slice(1, -1);
  }
  const lowerValue = trimmed.toLowerCase();
  if (lowerValue.length <= 9 && lowerValue in SPECIAL_VALUES) {
    return SPECIAL_VALUES[lowerValue];
  }
  if (!JSON_SIGNATURE.test(trimmed)) {
    if (strict) {
      throw new SyntaxError("[better-json] Invalid JSON");
    }
    return value;
  }
  const hasProtoPattern = Object.entries(PROTO_POLLUTION_PATTERNS).some(
    ([key, pattern]) => {
      const matches = pattern.test(trimmed);
      if (matches && warnings) {
        console.warn(
          `[better-json] Detected potential prototype pollution attempt using ${key} pattern`
        );
      }
      return matches;
    }
  );
  if (hasProtoPattern && strict) {
    throw new Error(
      "[better-json] Potential prototype pollution attempt detected"
    );
  }
  try {
    const secureReviver = (key, value2) => {
      if (key === "__proto__" || key === "constructor" && value2 && typeof value2 === "object" && "prototype" in value2) {
        if (warnings) {
          console.warn(
            `[better-json] Dropping "${key}" key to prevent prototype pollution`
          );
        }
        return void 0;
      }
      if (parseDates && typeof value2 === "string") {
        const date = parseISODate(value2);
        if (date) {
          return date;
        }
      }
      return reviver ? reviver(key, value2) : value2;
    };
    return JSON.parse(trimmed, secureReviver);
  } catch (error) {
    if (strict) {
      throw error;
    }
    return value;
  }
}
function parseJSON(value, options = { strict: true }) {
  return betterJSONParse(value, options);
}

// ../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/shared/better-auth.Dj5-80xo.mjs
var redirectPlugin = {
  id: "redirect",
  name: "Redirect",
  hooks: {
    onSuccess(context) {
      if (context.data?.url && context.data?.redirect) {
        if (typeof window !== "undefined" && window.location) {
          if (window.location) {
            try {
              window.location.href = context.data.url;
            } catch {
            }
          }
        }
      }
    }
  }
};
function getSessionAtom($fetch) {
  const $signal = atom(false);
  const session = useAuthQuery($signal, "/get-session", $fetch, {
    method: "GET"
  });
  return {
    session,
    $sessionSignal: $signal
  };
}
var getClientConfig = (options) => {
  const isCredentialsSupported = "credentials" in Request.prototype;
  const baseURL = getBaseURL(options?.baseURL, options?.basePath);
  const pluginsFetchPlugins = options?.plugins?.flatMap((plugin) => plugin.fetchPlugins).filter((pl) => pl !== void 0) || [];
  const lifeCyclePlugin = {
    id: "lifecycle-hooks",
    name: "lifecycle-hooks",
    hooks: {
      onSuccess: options?.fetchOptions?.onSuccess,
      onError: options?.fetchOptions?.onError,
      onRequest: options?.fetchOptions?.onRequest,
      onResponse: options?.fetchOptions?.onResponse
    }
  };
  const { onSuccess, onError, onRequest, onResponse, ...restOfFetchOptions } = options?.fetchOptions || {};
  const $fetch = createFetch({
    baseURL,
    ...isCredentialsSupported ? { credentials: "include" } : {},
    method: "GET",
    jsonParser(text) {
      if (!text) {
        return null;
      }
      return parseJSON(text, {
        strict: false
      });
    },
    customFetchImpl: async (input, init) => {
      try {
        return await fetch(input, init);
      } catch (error) {
        return Response.error();
      }
    },
    ...restOfFetchOptions,
    plugins: [
      lifeCyclePlugin,
      ...restOfFetchOptions.plugins || [],
      ...options?.disableDefaultFetchPlugins ? [] : [redirectPlugin],
      ...pluginsFetchPlugins
    ]
  });
  const { $sessionSignal, session } = getSessionAtom($fetch);
  const plugins = options?.plugins || [];
  let pluginsActions = {};
  let pluginsAtoms = {
    $sessionSignal,
    session
  };
  let pluginPathMethods = {
    "/sign-out": "POST",
    "/revoke-sessions": "POST",
    "/revoke-other-sessions": "POST",
    "/delete-user": "POST"
  };
  const atomListeners = [
    {
      signal: "$sessionSignal",
      matcher(path) {
        return path === "/sign-out" || path === "/update-user" || path.startsWith("/sign-in") || path.startsWith("/sign-up") || path === "/delete-user" || path === "/verify-email";
      }
    }
  ];
  for (const plugin of plugins) {
    if (plugin.getAtoms) {
      Object.assign(pluginsAtoms, plugin.getAtoms?.($fetch));
    }
    if (plugin.pathMethods) {
      Object.assign(pluginPathMethods, plugin.pathMethods);
    }
    if (plugin.atomListeners) {
      atomListeners.push(...plugin.atomListeners);
    }
  }
  const $store = {
    notify: (signal) => {
      pluginsAtoms[signal].set(
        !pluginsAtoms[signal].get()
      );
    },
    listen: (signal, listener) => {
      pluginsAtoms[signal].subscribe(listener);
    },
    atoms: pluginsAtoms
  };
  for (const plugin of plugins) {
    if (plugin.getActions) {
      Object.assign(
        pluginsActions,
        plugin.getActions?.($fetch, $store, options)
      );
    }
  }
  return {
    pluginsActions,
    pluginsAtoms,
    pluginPathMethods,
    atomListeners,
    $fetch,
    $store
  };
};
function getMethod(path, knownPathMethods, args) {
  const method = knownPathMethods[path];
  const { fetchOptions, query, ...body } = args || {};
  if (method) {
    return method;
  }
  if (fetchOptions?.method) {
    return fetchOptions.method;
  }
  if (body && Object.keys(body).length > 0) {
    return "POST";
  }
  return "GET";
}
function createDynamicPathProxy(routes, client, knownPathMethods, atoms, atomListeners) {
  function createProxy(path = []) {
    return new Proxy(function() {
    }, {
      get(target, prop) {
        const fullPath = [...path, prop];
        let current = routes;
        for (const segment of fullPath) {
          if (current && typeof current === "object" && segment in current) {
            current = current[segment];
          } else {
            current = void 0;
            break;
          }
        }
        if (typeof current === "function") {
          return current;
        }
        return createProxy(fullPath);
      },
      apply: async (_, __, args) => {
        const routePath = "/" + path.map(
          (segment) => segment.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`)
        ).join("/");
        const arg = args[0] || {};
        const fetchOptions = args[1] || {};
        const { query, fetchOptions: argFetchOptions, ...body } = arg;
        const options = {
          ...fetchOptions,
          ...argFetchOptions
        };
        const method = getMethod(routePath, knownPathMethods, arg);
        return await client(routePath, {
          ...options,
          body: method === "GET" ? void 0 : {
            ...body,
            ...options?.body || {}
          },
          query: query || options?.query,
          method,
          async onSuccess(context) {
            await options?.onSuccess?.(context);
            const matches = atomListeners?.find((s) => s.matcher(routePath));
            if (!matches) return;
            const signal = atoms[matches.signal];
            if (!signal) return;
            const val = signal.get();
            setTimeout(() => {
              signal.set(!val);
            }, 10);
          }
        });
      }
    });
  }
  return createProxy();
}

// ../node_modules/.bun/better-auth@1.3.5+7c17c985f1580bb6/node_modules/better-auth/dist/client/react/index.mjs
var import_react = __toESM(require_react(), 1);
function useStore(store, options = {}) {
  let snapshotRef = (0, import_react.useRef)(store.get());
  const { keys, deps = [store, keys] } = options;
  let subscribe = (0, import_react.useCallback)((onChange) => {
    const emitChange = (value) => {
      if (snapshotRef.current === value) return;
      snapshotRef.current = value;
      onChange();
    };
    emitChange(store.value);
    if (keys?.length) {
      return listenKeys(store, keys, emitChange);
    }
    return store.listen(emitChange);
  }, deps);
  let get = () => snapshotRef.current;
  return (0, import_react.useSyncExternalStore)(subscribe, get, get);
}
function getAtomKey(str) {
  return `use${capitalizeFirstLetter(str)}`;
}
function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
function createAuthClient(options) {
  const {
    pluginPathMethods,
    pluginsActions,
    pluginsAtoms,
    $fetch,
    $store,
    atomListeners
  } = getClientConfig(options);
  let resolvedHooks = {};
  for (const [key, value] of Object.entries(pluginsAtoms)) {
    resolvedHooks[getAtomKey(key)] = () => useStore(value);
  }
  const routes = {
    ...pluginsActions,
    ...resolvedHooks,
    $fetch,
    $store
  };
  const proxy = createDynamicPathProxy(
    routes,
    $fetch,
    pluginPathMethods,
    pluginsAtoms,
    atomListeners
  );
  return proxy;
}
export {
  capitalizeFirstLetter,
  createAuthClient,
  useStore
};
//# sourceMappingURL=better-auth_react.js.map
