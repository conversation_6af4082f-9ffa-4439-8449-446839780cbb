import { AgGridReact } from "ag-grid-react";
import React, { useRef } from "react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import { clsx } from "@/lib/utils";
import { usePersistStore } from "../../app/store/stores";

const TableTabs = ({
    currentTab,
    setCurrentTab,
    tabs,
}: {
    currentTab: string;
    setCurrentTab: (tab: string) => void;
    tabs: { name: string; value: string; current: boolean }[];
}) => {
    return (
        <div className="flex w-full divide-x-2 divide-slate-600 rounded-lg border-slate-600 border-x border-t">
            {tabs.map((tab, tabIdx) => (
                <button
                    key={tab.name}
                    aria-current={tab.current ? "page" : undefined}
                    className={clsx(
                        tab.current ? "text-gray-900" : "text-gray-500",
                        tabIdx === 0 ? "rounded-tl-lg" : "rounded-tr-lg",
                        tabIdx === tabs.length - 1 ? "" : "",
                        "group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-slate-800 dark:text-white"
                    )}
                    onClick={() => {
                        setCurrentTab(tab.value);
                    }}
                >
                    <span className="text-stroke-sm">{tab.name}</span>
                    <span
                        aria-hidden="true"
                        className={clsx(
                            tab.current ? "bg-indigo-600" : "bg-slate-700",
                            "absolute inset-x-0 bottom-0 h-[0.15rem]"
                        )}
                    />
                </button>
            ))}
        </div>
    );
};

const DataTable = ({
    dataList,
    colDefs,
    initialFilter,
    isLoading,
    rowHeight = 80,
    currentTab,
    setCurrentTab,
    tabs,
    customGridRef,
    keyProp = undefined,
    rowBuffer = 20,
}) => {
    const { persistedTablePageSize, setPersistedTablePageSize } = usePersistStore();
    const gridRef = useRef(null);

    const defaultColDef = {
        flex: 1,
        sortable: true,
        filter: true,
        resizable: true,
        cellClass: "px-1.5! md:px-2! 2xl:px-6!",
        floatingFilter: true,
        suppressHeaderMenuButton: true,
        suppressMovable: true,
        filterParams: { maxNumConditions: 1 },
    };

    const paginationPageSizeSelector = [10, 30, 50];

    const onPaginationChanged = (event) => {
        if (!persistedTablePageSize) return;
        if (event.api) {
            if (event.newPageSize === true) {
                const pageSize = event.api.paginationGetPageSize();
                if (paginationPageSizeSelector.includes(pageSize)) {
                    if (pageSize !== persistedTablePageSize) {
                        setPersistedTablePageSize(pageSize);
                    }
                }
            }
        }
    };

    return (
        <div className="ag-theme-quartz-dark rounded-t-lg 2xl:p-2" style={{ width: "100%", overflow: "auto" }}>
            {/* <MarketFilters gridRef={gridRef} setOpenModal={setOpenModal} /> */}
            {tabs && <TableTabs setCurrentTab={setCurrentTab} currentTab={currentTab} tabs={tabs} />}

            <AgGridReact
                key={keyProp}
                ref={customGridRef || gridRef}
                suppressCellFocus
                suppressRowHoverHighlight
                pagination
                rowBuffer={rowBuffer}
                rowData={dataList}
                columnDefs={colDefs}
                defaultColDef={defaultColDef}
                domLayout={"autoHeight"}
                rowHeight={rowHeight}
                paginationPageSizeSelector={paginationPageSizeSelector}
                paginationPageSize={persistedTablePageSize || 10}
                onPaginationChanged={(event) => onPaginationChanged(event)}
                onFirstDataRendered={initialFilter}
            />
        </div>
    );
};

export default DataTable;

// const MarketFilters = ({ gridRef, setOpenModal }) => {
//   const onFilterTextBoxChanged = useCallback(() => {
//     gridRef.current.api.setGridOption(
//       "quickFilterText",
//       document.getElementById("filter-item-name-box").value,
//     );
//   }, []);

//   return (
//     <div className="text-left flex items-center w-full gap-4">
//       <div className="w-40 pb-2">
//         <span className="font-body font-medium text-xs ml-3">Item Type</span>
//         <select
//           id="filter-item-name-box"
//           onChange={onFilterTextBoxChanged}
//           className="ml-2 rounded-md border mt-1 border-gray-300 dark:bg-gray-900 dark:border-gray-500 dark:text-white text-left text-sm md:w-52 font-medium text-gray-700 shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:text-base"
//         >
//           <option value="">Any</option>
//           <option value="crafting">Crafting Materials</option>
//           <option value="weapon">Melee Weapon</option>
//           <option value="ranged">Ranged Weapon</option>
//           <option value="offhand">Offhand</option>
//           <option value="shield">Shield</option>
//           <option value="head">Head</option>
//           <option value="chest">Chest</option>
//           <option value="hands">Gloves</option>
//           <option value="legs">Legs</option>
//           <option value="feet">Feet</option>
//           <option value="finger">Ring</option>
//           <option value="consumable">Consumable</option>
//           <option value="recipe">Recipe</option>
//           <option value="special">Special</option>
//           <option value="Junk">Junk</option>
//         </select>
//       </div>
//       {/* <div>
//         <p className="font-body font-medium text-xs mb-1 -mt-2">Level Range</p>
//         <div className="flex gap-1">
//           <input
//             type="number"
//             id="filter-min-level-box"
//             name="filter-min-level-box"
//             className="block w-full min-w-0 flex-1 text-xs text-center rounded-md border-gray-300 dark:border-gray-500 px-3 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-white"
//             placeholder={1}
//             min={1}
//             step={1}
//             max={35}
//             onChange={onFilterTextBoxChanged}
//           />
//           <input
//             type="number"
//             id="filter-max-level-box"
//             name="filter-max-level-box"
//             className="block w-full min-w-0 flex-1 text-xs text-center rounded-md border-gray-300 dark:border-gray-500 px-3 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-white"
//             placeholder={35}
//             min={1}
//             step={1}
//             max={35}
//             onChange={onFilterTextBoxChanged}
//           />
//         </div>
//       </div> */}
//       <div className="flex ml-auto mr-2">
//         <Button
//           className="md:min-w-32 md:text-base! text-xs!"
//           onClick={() => setOpenModal(true)}
//           variant="primary"
//         >
//           Sell Item
//         </Button>
//       </div>
//     </div>
//   );
// };
