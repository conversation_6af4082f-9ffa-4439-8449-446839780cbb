import { isLoggedInA<PERSON>, canMakeState<PERSON><PERSON><PERSON><PERSON>uth, publicAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as UserController from "./user.controller.js";
import userSchema from "./user.validation.js";
import * as MissionHelper from "../mission/mission.helpers.js";
import * as UserRepository from "../../repositories/user.repository.js";
import { getNow } from "../../utils/dateHelpers.js";
import { ExtUserModel } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";

const handleMissionCompletion = async (user: ExtUserModel) => {
    try {
        if (!user) return;
        const currentTime = getNow();
        if (user?.missionEnds && currentTime.getTime() > Number(user.missionEnds)) {
            await MissionHelper.CompleteMission(user.id);
        }
    } catch (error) {
        LogErrorStack({ error: error, message: `Error completing mission for user ${user.id}` });
    }
};

const handleDailyFatigueReset = async (user: ExtUserModel) => {
    try {
        if (user) {
            // Check if daily fatigue should be reset
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const lastReset = user.lastFatigueReset ? new Date(user.lastFatigueReset) : null;
            const shouldReset = !lastReset || lastReset < today;

            if (shouldReset) {
                // Reset daily fatigue and update last reset time
                await UserRepository.updateUserStats(
                    { id: user.id },
                    {
                        dailyFatigueUsed: 0,
                        lastFatigueReset: today,
                    }
                );

                // Update the user object so subsequent calls have the updated values
                user.dailyFatigueUsed = 0;
                user.lastFatigueReset = today;
            }
        }
    } catch (error) {
        LogErrorStack({ error: error, message: `Error resetting daily fatigue for user ${user?.id}` });
    }
};

export const userRouter = {
    // Get current user info (includes battle state, config version, etc.)
    // Includes background tasks: daily fatigue reset and mission completion
    getCurrentUserInfo: isLoggedInAuth.handler(async ({ context }) => {
        await handleDailyFatigueReset(context.user);
        await handleMissionCompletion(context.user);

        const response = await UserController.getCurrentUserInfo(context.user.id);
        return handleResponse(response);
    }),

    // Get public user profile info by user ID
    getUserInfo: isLoggedInAuth.input(userSchema.userInfoSchema).handler(async ({ input }) => {
        const response = await UserController.getUserInfo(input.id);
        return handleResponse(response);
    }),

    // Get user inventory
    getInventory: isLoggedInAuth.handler(async ({ context }) => {
        const response = await UserController.getInventory(context.user.id);
        return handleResponse(response);
    }),

    // Get equipped items
    getEquippedItems: isLoggedInAuth.handler(async ({ context }) => {
        const response = await UserController.getEquippedItems(context.user.id);
        return handleResponse(response);
    }),

    // Get tradeable inventory items
    getTradeableInventory: isLoggedInAuth.handler(async ({ context }) => {
        const response = await UserController.getTradeableInventory(context.user.id);
        return handleResponse(response);
    }),

    // Get user status effects
    getStatusEffects: isLoggedInAuth.handler(async ({ context }) => {
        const response = await UserController.getStatusEffects(context.user.id);
        return handleResponse(response);
    }),

    // Get user skills (optionally filtered by skill types)
    getSkills: isLoggedInAuth.input(userSchema.skillsQuerySchema).handler(async ({ input, context }) => {
        const response = await UserController.getAllUserSkills(context.user.id, input.skills);
        return handleResponse(response);
    }),

    // Get list of all users
    getUserList: isLoggedInAuth.input(userSchema.userListSchema).handler(async ({ input }) => {
        const response = await UserController.userList(input.page, input.limit);
        return handleResponse(response);
    }),

    // Equip an item
    equipItem: canMakeStateChangesAuth.input(userSchema.equipItemSchema).handler(async ({ input, context }) => {
        const response = await UserController.equipItem(context.user.id, input.userItemId);
        return handleResponse(response);
    }),

    // Unequip an item from a slot
    unequipItem: canMakeStateChangesAuth.input(userSchema.unequipItemSchema).handler(async ({ input, context }) => {
        const response = await UserController.unequipItem(context.user.id, input.slot);
        return handleResponse(response);
    }),

    // Use an item
    useItem: canMakeStateChangesAuth.input(userSchema.useItemSchema).handler(async ({ input, context }) => {
        const response = await UserController.useItem(context.user.id, input.userItemId);
        return handleResponse(response);
    }),

    // Train a stat (existing)
    train: canMakeStateChangesAuth.input(userSchema.trainSchema).handler(async ({ input, context }) => {
        const response = await UserController.train(context.user, input.stat, input.focusAmount);
        return handleResponse(response);
    }),

    // Get game configuration (public endpoint)
    getGameConfig: publicAuth.handler(async () => {
        const response = await UserController.getGameConfig();
        return handleResponse(response);
    }),

    // Link Discord account
    linkDiscord: isLoggedInAuth.input(userSchema.linkDiscordSchema).handler(async ({ input, context }) => {
        const response = await UserController.linkDiscord(input.token, context.user.id);
        return handleResponse(response);
    }),

    // Set last read news ID
    setLastNewsIDRead: isLoggedInAuth.input(userSchema.setLastNewsIDReadSchema).handler(async ({ input, context }) => {
        const response = await UserController.setLastNewsIDRead(context.user.id, input.id);
        return handleResponse(response);
    }),

    // Update profile details (about, username, avatar, banner)
    updateProfileDetails: isLoggedInAuth
        .input(userSchema.updateProfileDetailsSchema)
        .handler(async ({ input, context }) => {
            const response = await UserController.updateProfileDetailsORPC(context.user.id, input);
            return handleResponse(response);
        }),
};

export default userRouter;
