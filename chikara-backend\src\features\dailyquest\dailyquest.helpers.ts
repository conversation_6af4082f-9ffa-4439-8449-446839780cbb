import * as AchievementService from "../../core/achievement.service.js";
import * as FocusService from "../../core/focus.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import dailyQuests from "../../data/dailyQuests.js";
import * as DailyQuestRepository from "../../repositories/dailyquest.repository.js";
import type { DailyQuestModel, UserModel } from "../../lib/db.js";
import { QuestObjectiveTypes } from "../../types/quest.js";
import { getToday } from "../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import { Prisma, QuestProgressStatus } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
import { SendNotification } from "../../config/socket.js";
import { NotificationTypes } from "../../types/notification.js";

interface QuestTemplate {
    objectiveType: QuestObjectiveTypes;
    minTarget?: number;
    maxTarget?: number;
    minQuantity?: number;
    maxQuantity?: number;
    requiredLevel?: number;
    difficulty?: string;
    cashReward?: number;
    xpReward?: number;
    itemRewardId?: number;
    itemRewardQuantity?: number;
}

const getRandomInt = (min?: number, max?: number): number => {
    if (!min || !max) {
        return 1;
    }
    min = Math.ceil(min);
    max = Math.floor(max);

    // Generate a random whole number in the given range
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

const getRandomRewardDropId = () => {
    const { potentialDrops } = dailyQuests;

    const totalDropRate = potentialDrops.reduce((acc, drop) => acc + drop.dropRate, 0);

    const random = Math.random() * totalDropRate;

    let cumulativeRate = 0;
    for (const drop of potentialDrops) {
        cumulativeRate += drop.dropRate;
        if (random <= cumulativeRate) {
            return { dropId: drop.itemId, dropQuantity: drop.itemQuantity };
        }
    }

    // Default case (should never reach here if potentialDrops is not empty and rates are correct)
    return { dropId: 1, dropQuantity: 20 };
};

const getRandomVowel = () => {
    const vowels = ["a", "e", "i", "o", "u"];
    const randomIndex = Math.floor(Math.random() * vowels.length);
    return vowels[randomIndex];
};

const findSimilarCombatLevelUser = async (user: UserModel, range = 5) => {
    if (!user.combatLevel || typeof user.combatLevel !== "number") {
        return 10; // default to test account
    }

    const similarUser = await DailyQuestRepository.findSimilarLevelUser(user.id, user.combatLevel, user.level, range);
    return similarUser?.id || 10;
};

const assignQuestTarget = async (quest: QuestTemplate, currentUser: UserModel) => {
    switch (quest.objectiveType) {
        case "DEFEAT_SPECIFIC_PLAYER": {
            return await findSimilarCombatLevelUser(currentUser);
        }
        case "DEFEAT_NPC_IN_TURNS": {
            return getRandomInt(quest.minTarget, quest.maxTarget);
        }
        case "DEFEAT_NPC_WITH_LOW_DAMAGE": {
            return getRandomInt(quest.minTarget, quest.maxTarget);
        }
        default: {
            return null;
        }
    }
};

const assignQuestQuantity = (quest: QuestTemplate, currentUser: UserModel) => {
    let donationAmount = 0;
    switch (quest.objectiveType) {
        case "GAMBLING_SLOTS": {
            return currentUser.level * 800;
        }
        case "DONATE_TO_SHRINE": {
            donationAmount = (currentUser.cash + currentUser.bank_balance) * 0.1;
            return Math.round(donationAmount / 50) * 50;
        }
        default: {
            return getRandomInt(quest.minQuantity, quest.maxQuantity);
        }
    }
};

const assignQuestTargetAction = (objectiveType: QuestObjectiveTypes) => {
    if (objectiveType === "PVP_POST_BATTLE_CHOICE") {
        const actions = ["mug", "cripple", "leave"];
        return actions[Math.floor(Math.random() * actions.length)];
    }

    if (objectiveType === "DEFEAT_PLAYER_XNAME") {
        return getRandomVowel();
    }

    return null;
};

const generateRandomDailyQuests = (userLevel: number): QuestTemplate[] => {
    // Filter quests based on user level - only include quests where requiredLevel is undefined or <= userLevel
    const filterQuestsByLevel = (quests: readonly QuestTemplate[]): QuestTemplate[] => {
        return quests.filter((quest) => !quest.requiredLevel || quest.requiredLevel <= userLevel);
    };

    const easyQuests: QuestTemplate[] = filterQuestsByLevel(dailyQuests.easyQuests);
    const mediumQuests: QuestTemplate[] = filterQuestsByLevel(dailyQuests.mediumQuests);
    const hardQuests: QuestTemplate[] = filterQuestsByLevel(dailyQuests.hardQuests);

    const getRandomQuest = (quests: QuestTemplate[]): QuestTemplate | undefined => {
        if (quests.length === 0) {
            LogErrorStack({
                message: "Attempted to get random quest from empty list.",
                error: new Error("Attempted to get random quest from empty list."),
            });
            return undefined;
        }
        return quests[Math.floor(Math.random() * quests.length)];
    };

    const selectedQuests = [
        getRandomQuest(easyQuests),
        getRandomQuest(mediumQuests),
        getRandomQuest(hardQuests),
    ].filter((q): q is QuestTemplate => q !== undefined);

    return selectedQuests;
};

function getRandomReward(min: number, max: number) {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

function getRandomCashReward(level: number) {
    const min = level * 350;
    const max = level * 500;

    const cashReward = getRandomReward(min, max);
    const roundedCashReward = Math.round(cashReward / 100) * 100;

    if (roundedCashReward > 10_000) {
        return Math.round(roundedCashReward / 1000) * 1000;
    }
    return roundedCashReward;
}

const getRandomExpReward = (currentUser: UserModel, difficulty?: string) => {
    const xpForNextLevel = UserService.getXpForNextLevel(currentUser);
    let min = 1;
    let max = 1;
    if (difficulty === "medium") {
        min = xpForNextLevel * 0.08;
        max = xpForNextLevel * 0.14;
    } else {
        min = xpForNextLevel * 0.15;
        max = xpForNextLevel * 0.18;
    }

    const expReward = getRandomReward(min, max);
    return Math.round(expReward / 100) * 100;
};

const getRandomRewardType = () => {
    const rewards = ["exp", "item"];
    return rewards.sort(() => 0.5 - Math.random());
};

const assignQuestRewards = (quests: QuestTemplate[], currentUser: UserModel) => {
    const rewardTypes = getRandomRewardType();
    quests[0].cashReward = getRandomCashReward(currentUser.level);

    quests[1].difficulty = "medium";
    quests[2].difficulty = "hard";

    for (let i = 1; i < 3; i++) {
        const reward = rewardTypes[i - 1];

        if (reward === "item") {
            const itemReward = getRandomRewardDropId();
            quests[i].itemRewardId = itemReward.dropId;
            quests[i].itemRewardQuantity = itemReward.dropQuantity;
        }

        if (reward === "exp") {
            quests[i].xpReward = getRandomExpReward(currentUser, quests[i].difficulty);
        }
    }
};

const generateDailyQuests = async (userId: number, today: Date) => {
    const currentUser = await UserRepository.getUserById(userId);

    if (!currentUser) {
        throw new Error(`User with id ${userId} not found when generating daily quests`);
    }

    await DailyQuestRepository.destroyOldDailyQuests(currentUser.id, today);

    const randomQuests = generateRandomDailyQuests(currentUser.level);
    const questsWithRewards = structuredClone(randomQuests); // deep copy
    assignQuestRewards(questsWithRewards, currentUser);

    for (const quest of questsWithRewards) {
        const quantity = assignQuestQuantity(quest, currentUser);
        const target = await assignQuestTarget(quest, currentUser);
        const targetAction = assignQuestTargetAction(quest.objectiveType);

        const dailyQuestData: Prisma.daily_questCreateInput = {
            objectiveType: quest.objectiveType as QuestObjectiveTypes,
            user: { connect: { id: currentUser.id } },
            quantity,
            target,
            targetAction,
            questStatus: QuestProgressStatus.in_progress,
            count: 0,
            cashReward: quest.cashReward || 0,
            xpReward: quest.xpReward || 0,
            item: quest.itemRewardId ? { connect: { id: quest.itemRewardId } } : undefined,
            itemRewardQuantity: quest.itemRewardQuantity || null,
        };

        await DailyQuestRepository.createDailyQuest(dailyQuestData);
    }
};

export const GenerateDailyQuestsForUser = async (user: UserModel) => {
    try {
        const today = getToday();

        const existingDailies = await DailyQuestRepository.countDailyQuests(user.id, today);

        if (existingDailies === 3) return;

        if (existingDailies > 3) {
            logger.error(`User #${user.id} has more than 3 dailies! : ${existingDailies}`); // delete after testing
        }

        if (existingDailies > 0 && existingDailies < 3) {
            logger.error(`User #${user.id} has dailies but less than 3.`);
        }

        await generateDailyQuests(user.id, today);
    } catch (error) {
        LogErrorStack({ message: "Failed to generate daily quests:", error });
        throw new Error("Failed to generate daily quests");
    }
};

export const updateDailyQuest = async (dailyQuest: DailyQuestModel, amount: number) => {
    const updatedCount = dailyQuest.count + amount;
    const newStatus = updatedCount >= (dailyQuest.quantity ?? 1) ? "ready_to_complete" : dailyQuest.questStatus;

    const updates: { count: number; questStatus: QuestProgressStatus } = {
        count: updatedCount,
        questStatus: newStatus,
    };

    await DailyQuestRepository.updateDailyQuest(dailyQuest.id, updates);

    // Send toast notification when daily quest becomes ready to complete
    if (dailyQuest.userId && newStatus === "ready_to_complete" && dailyQuest.questStatus !== "ready_to_complete") {
        SendNotification(dailyQuest.userId, {
            type: NotificationTypes.quest_complete,
            details: JSON.stringify({ questType: "daily", objectiveType: dailyQuest.objectiveType }),
        });
    }
};

export const ApplyDailyQuestCompletion = async (dailyQuest: DailyQuestModel, user: UserModel) => {
    if (dailyQuest.cashReward > 0) {
        await UserService.updateUser(user.id, { cash: { increment: dailyQuest.cashReward } });
    }

    if (dailyQuest.xpReward > 0) {
        await UserService.AddXPToUser(user, dailyQuest.xpReward);
    }

    if (dailyQuest.itemRewardId) {
        await InventoryService.AddItemToUser({
            userId: user.id,
            itemId: dailyQuest.itemRewardId,
            amount: dailyQuest.itemRewardQuantity ?? 1,
            isTradeable: false,
        });
    }

    // Award focus for daily quest completion
    await FocusService.addDailyQuestFocus(user.id);

    await AchievementService.UpdateUserAchievement(user.id, "dailyQuestsCompleted");
};
