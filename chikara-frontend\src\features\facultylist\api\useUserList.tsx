import { api } from "@/helpers/api";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

interface UseUserListOptions {
    page?: number;
    limit?: number;
}

/**
 * Hook for fetching the list of faculty members with pagination support
 * @param options - Pagination options (page, limit)
 * @returns Query result with paginated faculty list data
 */
const useUserList = (options: UseUserListOptions = {}) => {
    return useQuery(
        api.user.getUserList.queryOptions({
            staleTime: 5 * 60 * 1000, // 5 minutes
            placeholderData: keepPreviousData,
            input: {
                page: options.page || 1,
                limit: options.limit || 50,
            },
        })
    );
};

export default useUserList;
